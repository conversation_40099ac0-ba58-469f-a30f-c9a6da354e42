package checker

import (
	"testing"
)

// TestIsMySQLTypeDatasource 测试MySQL类型数据源判断
func TestIsMySQLTypeDatasource(t *testing.T) {
	p := &permissionChecker{}

	testCases := []struct {
		dbType   string
		expected bool
	}{
		{"mysql", true},
		{"MySQL", true},
		{"MYSQL", true},
		{"tidb", true},
		{"TiDB", true},
		{"TIDB", true},
		{"tidb-proxy", true},
		{"TiDB-Proxy", true},
		{"TIDB-PROXY", true},
		{"oracle", false},
		{"postgresql", false},
		{"mongodb", false},
		{"", false},
	}

	for _, tc := range testCases {
		result := p.isMySQLTypeDatasource(tc.dbType)
		if result != tc.expected {
			t.Errorf("isMySQLTypeDatasource(%s) = %v, expected %v", tc.dbType, result, tc.expected)
		}
	}
}

// TestParseGrantString 测试权限字符串解析
func TestParseGrantString(t *testing.T) {
	p := &permissionChecker{}
	testUser := "testuser"
	testHost := "localhost"

	testCases := []struct {
		grantStr string
		expected *GrantInfo
	}{
		{
			"GRANT SELECT, INSERT ON *.* TO 'testuser'@'localhost'",
			&GrantInfo{
				User:       testUser,
				Host:       testHost,
				Privileges: []string{"SELECT", "INSERT"},
				OnObject:   "*.*",
			},
		},
		{
			"GRANT ALL PRIVILEGES ON `test`.* TO 'testuser'@'localhost'",
			&GrantInfo{
				User:       testUser,
				Host:       testHost,
				Privileges: []string{"ALL PRIVILEGES"},
				OnObject:   "`test`.*",
			},
		},
		{
			"GRANT RELOAD ON *.* TO 'testuser'@'localhost'",
			&GrantInfo{
				User:       testUser,
				Host:       testHost,
				Privileges: []string{"RELOAD"},
				OnObject:   "*.*",
			},
		},
		{
			"INVALID GRANT STRING",
			nil,
		},
		{
			"",
			nil,
		},
	}

	for i, tc := range testCases {
		result := p.parseGrantString(tc.grantStr, testUser, testHost)
		
		if tc.expected == nil {
			if result != nil {
				t.Errorf("Test case %d: parseGrantString(%s) should return nil", i+1, tc.grantStr)
			}
			continue
		}

		if result == nil {
			t.Errorf("Test case %d: parseGrantString(%s) returned nil, expected valid result", i+1, tc.grantStr)
			continue
		}

		if result.User != tc.expected.User || result.Host != tc.expected.Host {
			t.Errorf("Test case %d: User/Host mismatch. Got %s@%s, expected %s@%s", 
				i+1, result.User, result.Host, tc.expected.User, tc.expected.Host)
		}

		if result.OnObject != tc.expected.OnObject {
			t.Errorf("Test case %d: OnObject mismatch. Got %s, expected %s", 
				i+1, result.OnObject, tc.expected.OnObject)
		}

		if len(result.Privileges) != len(tc.expected.Privileges) {
			t.Errorf("Test case %d: Privileges length mismatch. Got %d, expected %d", 
				i+1, len(result.Privileges), len(tc.expected.Privileges))
			continue
		}

		for j, priv := range result.Privileges {
			if priv != tc.expected.Privileges[j] {
				t.Errorf("Test case %d: Privilege %d mismatch. Got %s, expected %s", 
					i+1, j, priv, tc.expected.Privileges[j])
			}
		}
	}
}

// TestObjectMatches 测试权限对象匹配
func TestObjectMatches(t *testing.T) {
	p := &permissionChecker{}

	testCases := []struct {
		grantObject    string
		requiredObject string
		expected       bool
	}{
		{"*.*", "`test`.`table1`", true},
		{"*.*", "`information_schema`.*", true},
		{"`test`.*", "`test`.`table1`", true},
		{"`test`.*", "`test2`.`table1`", false},
		{"`test`.`table1`", "`test`.`table1`", true},
		{"`test`.`table1`", "`test`.`table2`", false},
		{"`information_schema`.*", "`information_schema`.*", true},
		{"`information_schema`.*", "`test`.*", false},
	}

	for i, tc := range testCases {
		result := p.objectMatches(tc.grantObject, tc.requiredObject)
		if result != tc.expected {
			t.Errorf("Test case %d: objectMatches(%s, %s) = %v, expected %v", 
				i+1, tc.grantObject, tc.requiredObject, result, tc.expected)
		}
	}
}

// TestHasRequiredPrivilege 测试权限检查逻辑
func TestHasRequiredPrivilege(t *testing.T) {
	p := &permissionChecker{}

	// 创建模拟权限信息
	grants := []GrantInfo{
		{
			User:       "testuser",
			Host:       "localhost",
			Privileges: []string{"SELECT", "INSERT"},
			OnObject:   "*.*",
		},
		{
			User:       "testuser",
			Host:       "localhost", 
			Privileges: []string{"RELOAD"},
			OnObject:   "*.*",
		},
		{
			User:       "testuser",
			Host:       "localhost",
			Privileges: []string{"SELECT"},
			OnObject:   "`test`.*",
		},
	}

	testCases := []struct {
		required RequiredPrivilege
		expected bool
	}{
		{
			RequiredPrivilege{Privilege: "SELECT", Object: "`test`.`table1`", Description: "SELECT on test.table1"},
			true, // 匹配全局 SELECT 权限
		},
		{
			RequiredPrivilege{Privilege: "SELECT", Object: "`test2`.`table1`", Description: "SELECT on test2.table1"},
			true, // 匹配全局 SELECT 权限
		},
		{
			RequiredPrivilege{Privilege: "RELOAD", Object: "*.*", Description: "RELOAD privilege"},
			true, // 匹配 RELOAD 权限
		},
		{
			RequiredPrivilege{Privilege: "REPLICATION SLAVE", Object: "*.*", Description: "REPLICATION SLAVE privilege"},
			false, // 没有此权限
		},
		{
			RequiredPrivilege{Privilege: "DELETE", Object: "`test`.`table1`", Description: "DELETE on test.table1"},
			false, // 没有 DELETE 权限
		},
	}

	for i, tc := range testCases {
		result := p.hasRequiredPrivilege(grants, tc.required)
		if result != tc.expected {
			t.Errorf("Test case %d: hasRequiredPrivilege(%s) = %v, expected %v", 
				i+1, tc.required.Description, result, tc.expected)
		}
	}
}

// TestRemoveDuplicateStrings 测试去重功能
func TestRemoveDuplicateStrings(t *testing.T) {
	p := &permissionChecker{}

	testCases := []struct {
		input    []string
		expected []string
	}{
		{
			[]string{"a", "b", "c", "a", "b"},
			[]string{"a", "b", "c"},
		},
		{
			[]string{"SELECT", "INSERT", "SELECT", "DELETE"},
			[]string{"SELECT", "INSERT", "DELETE"},
		},
		{
			[]string{},
			[]string{},
		},
		{
			[]string{"unique"},
			[]string{"unique"},
		},
	}

	for i, tc := range testCases {
		result := p.removeDuplicateStrings(tc.input)
		
		if len(result) != len(tc.expected) {
			t.Errorf("Test case %d: length mismatch. Got %d, expected %d", 
				i+1, len(result), len(tc.expected))
			continue
		}

		// 将结果转换为map以便比较（因为去重后的顺序可能不同）
		resultMap := make(map[string]bool)
		for _, item := range result {
			resultMap[item] = true
		}

		expectedMap := make(map[string]bool)
		for _, item := range tc.expected {
			expectedMap[item] = true
		}

		for item := range expectedMap {
			if !resultMap[item] {
				t.Errorf("Test case %d: missing item %s in result", i+1, item)
			}
		}

		for item := range resultMap {
			if !expectedMap[item] {
				t.Errorf("Test case %d: unexpected item %s in result", i+1, item)
			}
		}
	}
}

// TestContainsString 测试字符串包含检查
func TestContainsString(t *testing.T) {
	p := &permissionChecker{}

	testCases := []struct {
		slice    []string
		item     string
		expected bool
	}{
		{
			[]string{"RELOAD privilege", "SELECT on table1", "REPLICATION CLIENT privilege"},
			"reload",
			true,
		},
		{
			[]string{"RELOAD privilege", "SELECT on table1", "REPLICATION CLIENT privilege"},
			"REPLICATION",
			true,
		},
		{
			[]string{"RELOAD privilege", "SELECT on table1"},
			"INSERT",
			false,
		},
		{
			[]string{},
			"test",
			false,
		},
	}

	for i, tc := range testCases {
		result := p.containsString(tc.slice, tc.item)
		if result != tc.expected {
			t.Errorf("Test case %d: containsString(%v, %s) = %v, expected %v", 
				i+1, tc.slice, tc.item, result, tc.expected)
		}
	}
}
