package checker

import (
	"context"
	"database/sql"
	"fmt"
	"strings"

	"gitee.com/pingcap_enterprise/tms/pkg/mysql/core"
	"gitee.com/pingcap_enterprise/tms/server/message"
	"gitee.com/pingcap_enterprise/tms/server/models"
	"gitee.com/pingcap_enterprise/tms/server/models/channel"
	"gitee.com/pingcap_enterprise/tms/server/models/datasource"
	"gitee.com/pingcap_enterprise/tms/util/database"
	"gitee.com/pingcap_enterprise/tms/util/log"
)

// permissionChecker 数据源权限检查器
type permissionChecker struct{}

// RequiredPrivilege 表示所需的权限信息
type RequiredPrivilege struct {
	Privilege   string // 权限名称
	Object      string // 权限对象（数据库名、表名或*）
	Description string // 权限描述
}

// GrantInfo 表示用户权限信息
type GrantInfo struct {
	User       string   // 用户名
	Host       string   // 主机
	Privileges []string // 权限列表
	OnObject   string   // 权限对象
}

// NewPermissionChecker 创建数据源权限检查器实例
func NewPermissionChecker() PermissionChecker {
	return &permissionChecker{}
}

// Check 检查数据源权限
func (p *permissionChecker) Check(ctx context.Context, channelId int, progress *core.CheckProgress, checkType core.CheckType) {
	log.Infof("PermissionChecker: Starting to check data source permissions for channel: %d", channelId)

	progress.UpdateCheckItemByType(checkType, "running", 0)

	// 获取通道信息
	channel, err := models.GetChannelReaderWriter().GetChannel(ctx, channelId)
	if err != nil {
		log.Errorf("PermissionChecker: Failed to get channel information, channelId: %d, error: %v", channelId, err)
		progress.UpdateCheckItemErrorByType(checkType, "Failed to get channel information", err.Error(), "Please check if the channel ID is correct")
		return
	}

	progress.UpdateCheckItemByType(checkType, "running", 10)

	// 获取通道关联的数据源
	channelDatasources, err := models.GetChannelReaderWriter().GetChannelDatasourcesByChannelId(ctx, channelId)
	if err != nil {
		log.Errorf("PermissionChecker: Failed to get channel datasources, channelId: %d, error: %v", channelId, err)
		progress.UpdateCheckItemErrorByType(checkType, "Failed to get channel datasources", err.Error(), "Please check channel configuration")
		return
	}

	progress.UpdateCheckItemByType(checkType, "running", 20)

	// 检查每个数据源的权限
	var allPermissionResults []message.SourcePermissionInfo
	totalDatasources := len(channelDatasources)

	for i, channelDs := range channelDatasources {
		// 获取数据源详细信息
		ds, err := models.GetDatasourceReaderWriter().Get(ctx, channelDs.DataSourceId)
		if err != nil {
			log.Errorf("PermissionChecker: Failed to get datasource info, datasourceId: %d, error: %v", channelDs.DataSourceId, err)
			continue
		}

		// 只检查MySQL/TiDB类型的数据源
		if !p.isMySQLTypeDatasource(ds.DbType) {
			log.Infof("PermissionChecker: Skipping non-MySQL datasource: %s (type: %s)", ds.DatasourceName, ds.DbType)
			continue
		}

		log.Infof("PermissionChecker: Checking permissions for datasource: %s", ds.DatasourceName)

		// 检查当前数据源权限
		permissionResult := p.checkDatasourcePermissions(ctx, channel, ds)
		allPermissionResults = append(allPermissionResults, permissionResult)

		// 更新进度
		currentProgress := 20 + int(float64(i+1)/float64(totalDatasources)*70)
		progress.UpdateCheckItemByType(checkType, "running", currentProgress)
	}

	// 分析整体检查结果
	p.analyzeOverallResult(progress, checkType, allPermissionResults)

	log.Infof("PermissionChecker: Data source permission check completed for channel: %d", channelId)
}

// isMySQLTypeDatasource 判断是否为MySQL类型的数据源
func (p *permissionChecker) isMySQLTypeDatasource(dbType string) bool {
	mysqlTypes := []string{"mysql", "tidb", "tidb-proxy"}
	dbTypeLower := strings.ToLower(dbType)
	for _, mysqlType := range mysqlTypes {
		if dbTypeLower == mysqlType {
			return true
		}
	}
	return false
}

// checkDatasourcePermissions 检查单个数据源的权限
func (p *permissionChecker) checkDatasourcePermissions(ctx context.Context, channelInfo *channel.ChannelInformation, ds *datasource.Datasource) message.SourcePermissionInfo {
	result := message.SourcePermissionInfo{
		SourceName:        ds.DatasourceName,
		HasPermission:     false,
		MissingPrivileges: []string{},
	}

	// 创建数据库连接
	db, err := p.createDatabaseConnection(ds)
	if err != nil {
		log.Errorf("PermissionChecker: Failed to connect to datasource %s: %v", ds.DatasourceName, err)
		result.MissingPrivileges = append(result.MissingPrivileges, fmt.Sprintf("CONNECTION_FAILED: %s", err.Error()))
		return result
	}
	defer func() {
		if closeErr := db.Close(); closeErr != nil {
			log.Warnf("PermissionChecker: Failed to close database connection for %s: %v", ds.DatasourceName, closeErr)
		}
	}()

	// 获取当前用户信息
	currentUser, currentHost, err := p.getCurrentUser(ctx, db)
	if err != nil {
		log.Errorf("PermissionChecker: Failed to get current user for %s: %v", ds.DatasourceName, err)
		result.MissingPrivileges = append(result.MissingPrivileges, fmt.Sprintf("USER_INFO_FAILED: %s", err.Error()))
		return result
	}

	log.Infof("PermissionChecker: Checking permissions for user %s@%s on datasource %s", currentUser, currentHost, ds.DatasourceName)

	// 获取用户权限信息
	grants, err := p.getUserGrants(ctx, db, currentUser, currentHost)
	if err != nil {
		log.Errorf("PermissionChecker: Failed to get user grants for %s@%s: %v", currentUser, currentHost, err)
		result.MissingPrivileges = append(result.MissingPrivileges, fmt.Sprintf("GRANTS_QUERY_FAILED: %s", err.Error()))
		return result
	}

	// 获取需要同步的表信息
	requiredTables, err := p.getRequiredTables(ctx, channelInfo.ChannelId)
	if err != nil {
		log.Errorf("PermissionChecker: Failed to get required tables for channel %d: %v", channelInfo.ChannelId, err)
		result.MissingPrivileges = append(result.MissingPrivileges, fmt.Sprintf("TABLE_INFO_FAILED: %s", err.Error()))
		return result
	}

	// 检查所需权限
	missingPrivileges := p.checkRequiredPrivileges(grants, requiredTables)
	result.MissingPrivileges = missingPrivileges
	result.HasPermission = len(missingPrivileges) == 0

	return result
}

// createDatabaseConnection 创建数据库连接
func (p *permissionChecker) createDatabaseConnection(ds *datasource.Datasource) (*sql.DB, error) {
	config := database.MySQLConfig{
		User:          ds.UserName,
		Password:      ds.PasswordValue, // 使用明文密码进行连接测试
		Host:          ds.HostIp,
		Port:          ds.HostPort,
		Database:      ds.DbName,
		Charset:       ds.Charset,
		ConnectParams: ds.ConnectParams,
	}

	// 验证配置
	if err := config.Validate(); err != nil {
		return nil, fmt.Errorf("invalid MySQL config: %w", err)
	}

	// 创建连接器
	connector := database.NewMySQLConnector(config)

	// 打开数据库连接
	return database.OpenMySQL(connector, database.WithPing())
}

// getCurrentUser 获取当前连接的用户信息
func (p *permissionChecker) getCurrentUser(ctx context.Context, db *sql.DB) (string, string, error) {
	query := "SELECT USER(), CONNECTION_ID()"

	row := db.QueryRowContext(ctx, query)
	var userHost string
	var connectionId int64

	err := row.Scan(&userHost, &connectionId)
	if err != nil {
		return "", "", fmt.Errorf("failed to get current user: %w", err)
	}

	// 解析 user@host 格式
	parts := strings.Split(userHost, "@")
	if len(parts) != 2 {
		return "", "", fmt.Errorf("invalid user@host format: %s", userHost)
	}

	user := parts[0]
	host := parts[1]

	return user, host, nil
}

// getUserGrants 获取用户的权限信息
func (p *permissionChecker) getUserGrants(ctx context.Context, db *sql.DB, user, host string) ([]GrantInfo, error) {
	var grants []GrantInfo

	// 查询用户权限
	query := fmt.Sprintf("SHOW GRANTS FOR '%s'@'%s'", user, host)
	rows, err := db.QueryContext(ctx, query)
	if err != nil {
		return nil, fmt.Errorf("failed to query user grants: %w", err)
	}
	defer rows.Close()

	for rows.Next() {
		var grantStr string
		if err := rows.Scan(&grantStr); err != nil {
			continue
		}

		// 解析权限字符串
		grantInfo := p.parseGrantString(grantStr, user, host)
		if grantInfo != nil {
			grants = append(grants, *grantInfo)
		}
	}

	return grants, nil
}

// parseGrantString 解析权限字符串
func (p *permissionChecker) parseGrantString(grantStr, user, host string) *GrantInfo {
	// GRANT 权限列表 ON 对象 TO 用户
	// 示例: GRANT SELECT, INSERT ON *.* TO 'user'@'host'
	// 示例: GRANT SELECT ON `database`.* TO 'user'@'host'

	grantStr = strings.TrimSpace(grantStr)
	if !strings.HasPrefix(strings.ToUpper(grantStr), "GRANT ") {
		return nil
	}

	// 移除 "GRANT " 前缀
	grantStr = grantStr[6:]

	// 查找 " ON " 分隔符
	onIndex := strings.Index(strings.ToUpper(grantStr), " ON ")
	if onIndex == -1 {
		return nil
	}

	privilegesPart := strings.TrimSpace(grantStr[:onIndex])
	remaining := strings.TrimSpace(grantStr[onIndex+4:]) // +4 for " ON "

	// 查找 " TO " 分隔符
	toIndex := strings.Index(strings.ToUpper(remaining), " TO ")
	if toIndex == -1 {
		return nil
	}

	objectPart := strings.TrimSpace(remaining[:toIndex])

	// 解析权限列表
	privileges := make([]string, 0)
	privs := strings.Split(privilegesPart, ",")
	for _, priv := range privs {
		priv = strings.TrimSpace(priv)
		if priv != "" {
			privileges = append(privileges, strings.ToUpper(priv))
		}
	}

	return &GrantInfo{
		User:       user,
		Host:       host,
		Privileges: privileges,
		OnObject:   objectPart,
	}
}

// getRequiredTables 获取需要同步的表信息
func (p *permissionChecker) getRequiredTables(ctx context.Context, channelId int) ([]string, error) {
	// 获取通道关联的表信息
	tables, err := models.GetChannelReaderWriter().GetChannelSchemaTablesByChannelId(ctx, channelId)
	if err != nil {
		return nil, fmt.Errorf("failed to get channel schema tables: %w", err)
	}

	var tableNames []string
	schemaTableMap := make(map[string]bool)

	for _, table := range tables {
		// 构建 schema.table 格式
		fullTableName := fmt.Sprintf("`%s`.`%s`", table.SchemaNameS, table.TableNameS)
		if !schemaTableMap[fullTableName] {
			tableNames = append(tableNames, fullTableName)
			schemaTableMap[fullTableName] = true
		}
	}

	return tableNames, nil
}

// checkRequiredPrivileges 检查所需权限
func (p *permissionChecker) checkRequiredPrivileges(grants []GrantInfo, requiredTables []string) []string {
	var missingPrivileges []string

	// 定义需要检查的权限
	requiredPrivileges := []RequiredPrivilege{
		{Privilege: "SELECT", Object: "`information_schema`.*", Description: "SELECT on information_schema"},
		{Privilege: "RELOAD", Object: "*.*", Description: "RELOAD privilege"},
		{Privilege: "REPLICATION CLIENT", Object: "*.*", Description: "REPLICATION CLIENT privilege"},
		{Privilege: "REPLICATION SLAVE", Object: "*.*", Description: "REPLICATION SLAVE privilege"},
	}

	// 为每个需要同步的表添加SELECT权限检查
	for _, tableName := range requiredTables {
		requiredPrivileges = append(requiredPrivileges, RequiredPrivilege{
			Privilege:   "SELECT",
			Object:      tableName,
			Description: fmt.Sprintf("SELECT on %s", tableName),
		})
	}

	// 检查每个所需权限
	for _, required := range requiredPrivileges {
		if !p.hasRequiredPrivilege(grants, required) {
			missingPrivileges = append(missingPrivileges, required.Description)
		}
	}

	return missingPrivileges
}

// hasRequiredPrivilege 检查是否拥有指定权限
func (p *permissionChecker) hasRequiredPrivilege(grants []GrantInfo, required RequiredPrivilege) bool {
	for _, grant := range grants {
		// 检查是否有ALL权限
		for _, priv := range grant.Privileges {
			if priv == "ALL" || priv == "ALL PRIVILEGES" {
				// 检查对象是否匹配
				if p.objectMatches(grant.OnObject, required.Object) {
					return true
				}
			}
		}

		// 检查具体权限
		for _, priv := range grant.Privileges {
			if priv == required.Privilege {
				// 检查对象是否匹配
				if p.objectMatches(grant.OnObject, required.Object) {
					return true
				}
			}
		}
	}

	return false
}

// objectMatches 检查权限对象是否匹配
func (p *permissionChecker) objectMatches(grantObject, requiredObject string) bool {
	// 标准化对象名称
	grantObject = strings.TrimSpace(grantObject)
	requiredObject = strings.TrimSpace(requiredObject)

	// 全局权限 *.*
	if grantObject == "*.*" {
		return true
	}

	// 精确匹配
	if grantObject == requiredObject {
		return true
	}

	// 处理数据库级权限匹配
	// 如果 grantObject 是 `database`.*，requiredObject 是 `database`.`table`
	if strings.HasSuffix(grantObject, ".*") {
		dbPart := strings.TrimSuffix(grantObject, ".*")
		if strings.HasPrefix(requiredObject, dbPart+".") {
			return true
		}
	}

	return false
}

// analyzeOverallResult 分析整体检查结果
func (p *permissionChecker) analyzeOverallResult(progress *core.CheckProgress, checkType core.CheckType, results []message.SourcePermissionInfo) {
	if len(results) == 0 {
		progress.UpdateCheckItemByType(checkType, "success", 100)
		progress.AddCheckItemDetailByType(checkType, "info", "No MySQL/TiDB datasources found to check permissions", message.SourcePermissionInfo{}, "")
		return
	}

	var hasErrors bool
	var totalMissingPrivileges []string

	for _, result := range results {
		// 记录每个数据源的检查结果
		if result.HasPermission {
			progress.AddCheckItemDetailByType(checkType, "success",
				fmt.Sprintf("Datasource '%s' has all required permissions", result.SourceName),
				result, "")
		} else {
			hasErrors = true
			progress.AddCheckItemDetailByType(checkType, "error",
				fmt.Sprintf("Datasource '%s' is missing required permissions", result.SourceName),
				result,
				fmt.Sprintf("Please grant the following privileges: %s", strings.Join(result.MissingPrivileges, ", ")))

			// 收集所有缺失的权限
			totalMissingPrivileges = append(totalMissingPrivileges, result.MissingPrivileges...)
		}
	}

	// 设置整体状态
	if hasErrors {
		progress.UpdateCheckItemByType(checkType, "error", 100)

		// 生成权限修复建议
		suggestion := p.generatePermissionSuggestion(results)
		progress.AddCheckItemDetailByType(checkType, "warn",
			"Some datasources are missing required permissions",
			message.SourcePermissionInfo{
				SourceName:        "Overall",
				HasPermission:     false,
				MissingPrivileges: p.removeDuplicateStrings(totalMissingPrivileges),
			},
			suggestion)
	} else {
		progress.UpdateCheckItemByType(checkType, "success", 100)
		progress.AddCheckItemDetailByType(checkType, "info",
			"All datasources have the required permissions",
			message.SourcePermissionInfo{
				SourceName:    "Overall",
				HasPermission: true,
			}, "")
	}
}

// generatePermissionSuggestion 生成权限修复建议
func (p *permissionChecker) generatePermissionSuggestion(results []message.SourcePermissionInfo) string {
	suggestions := []string{
		"To fix the permission issues, please execute the following SQL commands on your MySQL/TiDB instances:",
		"",
	}

	for _, result := range results {
		if !result.HasPermission && len(result.MissingPrivileges) > 0 {
			suggestions = append(suggestions, fmt.Sprintf("-- For datasource: %s", result.SourceName))
			suggestions = append(suggestions, "-- Replace 'username'@'host' with the actual user and host")

			// 根据缺失的权限生成具体的GRANT语句
			if p.containsString(result.MissingPrivileges, "RELOAD") {
				suggestions = append(suggestions, "GRANT RELOAD ON *.* TO 'username'@'host';")
			}
			if p.containsString(result.MissingPrivileges, "REPLICATION CLIENT") {
				suggestions = append(suggestions, "GRANT REPLICATION CLIENT ON *.* TO 'username'@'host';")
			}
			if p.containsString(result.MissingPrivileges, "REPLICATION SLAVE") {
				suggestions = append(suggestions, "GRANT REPLICATION SLAVE ON *.* TO 'username'@'host';")
			}
			if p.containsString(result.MissingPrivileges, "information_schema") {
				suggestions = append(suggestions, "GRANT SELECT ON information_schema.* TO 'username'@'host';")
			}

			// 添加表级权限建议
			var hasTablePermissions bool
			for _, priv := range result.MissingPrivileges {
				if strings.Contains(priv, "SELECT on `") {
					if !hasTablePermissions {
						suggestions = append(suggestions, "-- Grant SELECT on required tables:")
						hasTablePermissions = true
					}
					// 从权限描述中提取表名
					tableName := p.extractTableNameFromPrivilege(priv)
					if tableName != "" {
						suggestions = append(suggestions, fmt.Sprintf("GRANT SELECT ON %s TO 'username'@'host';", tableName))
					}
				}
			}

			suggestions = append(suggestions, "FLUSH PRIVILEGES;")
			suggestions = append(suggestions, "")
		}
	}

	return strings.Join(suggestions, "\n")
}

// containsString 检查切片中是否包含指定字符串（忽略大小写）
func (p *permissionChecker) containsString(slice []string, item string) bool {
	for _, s := range slice {
		if strings.Contains(strings.ToLower(s), strings.ToLower(item)) {
			return true
		}
	}
	return false
}

// extractTableNameFromPrivilege 从权限描述中提取表名
func (p *permissionChecker) extractTableNameFromPrivilege(privilege string) string {
	// 从 "SELECT on `database`.`table`" 中提取表名
	if strings.Contains(privilege, "SELECT on ") {
		parts := strings.Split(privilege, "SELECT on ")
		if len(parts) > 1 {
			return strings.TrimSpace(parts[1])
		}
	}
	return ""
}

// removeDuplicateStrings 去除字符串切片中的重复项
func (p *permissionChecker) removeDuplicateStrings(slice []string) []string {
	keys := make(map[string]bool)
	var result []string

	for _, item := range slice {
		if !keys[item] {
			keys[item] = true
			result = append(result, item)
		}
	}

	return result
}
