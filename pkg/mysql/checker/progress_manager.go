package checker

import (
	"context"
	"sync"
	"time"

	"gitee.com/pingcap_enterprise/tms/common/errors"
	"gitee.com/pingcap_enterprise/tms/pkg/mysql/core"
	"gitee.com/pingcap_enterprise/tms/server/message"
	"gitee.com/pingcap_enterprise/tms/util/log"
	"github.com/google/uuid"
)

// progressManager 检查进度管理器
type progressManager struct {
	checkProgress map[string]*core.CheckProgress
	progressMu    sync.RWMutex

	tableChecker      TableChecker
	sourceBindingChecker     SourceBindingChecker
	permissionChecker PermissionChecker
}

// TableChecker 表一致性检查接口
type TableChecker interface {
	Check(ctx context.Context, channelId int, progress *core.CheckProgress, checkType core.CheckType)
}

// SourceBindingChecker 数据源绑定检查接口
type SourceBindingChecker interface {
	Check(ctx context.Context, channelId int, masterAddr string, progress *core.CheckProgress, checkType core.CheckType)
}

// PermissionChecker 权限检查接口
type PermissionChecker interface {
	Check(ctx context.Context, channelId int, progress *core.CheckProgress, checkType core.CheckType)
}

// NewCheckManager 创建检查管理器实例
func NewCheckManager() core.CheckManager {
	return &progressManager{
		checkProgress:     make(map[string]*core.CheckProgress),
		tableChecker:      NewTableChecker(),
		sourceBindingChecker:     NewSourceBindingChecker(),
		permissionChecker: NewPermissionChecker(),
	}
}

// TriggerCheck 触发检查
func (m *progressManager) TriggerCheck(ctx context.Context, channelId int, masterAddr string) (string, error) {
	log.Infof("CheckManager: Triggering comprehensive check for channel: %d, masterAddr: %s", channelId, masterAddr)

	// 生成唯一RequestID
	requestID := uuid.New().String()

	// 初始化检查进度（基于 CheckType）
	var items []message.CheckItem
	for _, ct := range core.GetAllCheckTypes() {
		items = append(items, message.CheckItem{
			CheckType: string(ct),
			Name:      core.GetCheckTypeName(ct),
			Status:    "pending",
			Progress:  0,
			Details:   []message.CheckItemDetail{},
		})
	}

	progress := &core.CheckProgress{
		RequestID:       requestID,
		OverallStatus:   "pending",
		OverallProgress: 0,
		StartTime:       time.Now(),
		CheckItems:      items,
	}

	// 存储到内存
	m.progressMu.Lock()
	m.checkProgress[requestID] = progress
	m.progressMu.Unlock()

	// 启动后台检查
	go m.performComprehensiveCheck(ctx, channelId, masterAddr, progress)

	log.Infof("CheckManager: Check triggered, requestID: %s, channelId: %d, masterAddr: %s", requestID, channelId, masterAddr)
	return requestID, nil
}

// GetCheckStatus 获取检查状态
func (m *progressManager) GetCheckStatus(requestID string) (*core.CheckProgress, error) {
	m.progressMu.RLock()
	progress, exists := m.checkProgress[requestID]
	m.progressMu.RUnlock()

	if !exists {
		log.Errorf("CheckManager: RequestID not found: %s", requestID)
		return nil, errors.NewError(errors.TIMS_RECORD_NOT_EXIST, "检查请求不存在")
	}

	return progress, nil
}

// performComprehensiveCheck 执行全面检查
func (m *progressManager) performComprehensiveCheck(ctx context.Context, channelId int, masterAddr string, progress *core.CheckProgress) {
	defer func() {
		if r := recover(); r != nil {
			log.Errorf("CheckManager: Panic occurred during check process, requestID: %s, error: %v", progress.RequestID, r)
			m.updateOverallStatus(progress, "failed")
		}
	}()

	log.Infof("CheckManager: Starting comprehensive check, requestID: %s", progress.RequestID)

	// 更新状态为运行中
	m.updateOverallStatus(progress, "running")

	// 执行各项检查（基于 CheckType）
	m.tableChecker.Check(ctx, channelId, progress, core.CheckTypeTableConsistency)
	m.sourceBindingChecker.Check(ctx, channelId, masterAddr, progress, core.CheckTypeWorkerBinding)
	m.permissionChecker.Check(ctx, channelId, progress, core.CheckTypeSourcePermission)

	// 计算整体进度和状态
	m.calculateOverallProgress(progress)

	// 设置结束时间
	progress.Lock()
	now := time.Now()
	progress.EndTime = &now
	progress.Unlock()

	log.Infof("CheckManager: Comprehensive check completed, requestID: %s", progress.RequestID)
}

// CleanupExpiredProgress 清理过期的检查进度
func (m *progressManager) CleanupExpiredProgress(maxAge time.Duration) int {
	m.progressMu.Lock()
	defer m.progressMu.Unlock()

	cleaned := 0
	now := time.Now()

	for requestID, progress := range m.checkProgress {
		if now.Sub(progress.StartTime) > maxAge {
			delete(m.checkProgress, requestID)
			cleaned++
		}
	}

	if cleaned > 0 {
		log.Infof("CheckManager: Cleaned up %d expired check progress records", cleaned)
	}

	return cleaned
}

// updateOverallStatus 更新整体状态
func (m *progressManager) updateOverallStatus(progress *core.CheckProgress, status string) {
	progress.Lock()
	defer progress.Unlock()
	progress.OverallStatus = status
}

// calculateOverallProgress 计算整体进度
func (m *progressManager) calculateOverallProgress(progress *core.CheckProgress) {
	progress.Lock()
	defer progress.Unlock()

	totalProgress := 0
	completedItems := 0
	failedItems := 0

	for _, item := range progress.CheckItems {
		totalProgress += item.Progress
		if item.Status == "success" {
			completedItems++
		} else if item.Status == "failed" {
			failedItems++
		}
	}

	progress.OverallProgress = totalProgress / len(progress.CheckItems)

	if completedItems == len(progress.CheckItems) {
		progress.OverallStatus = "completed"
	} else if failedItems > 0 && completedItems+failedItems == len(progress.CheckItems) {
		progress.OverallStatus = "completed"
	}
}
