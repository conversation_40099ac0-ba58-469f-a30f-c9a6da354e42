package checker

import (
	"testing"
)

// TestNewSourceBindingChecker 测试数据源绑定检查器实例创建
func TestNewSourceBindingChecker(t *testing.T) {
	checker := NewSourceBindingChecker()
	if checker == nil {
		t.Error("NewSourceBindingChecker should return a non-nil instance")
	}

	// 验证实例类型
	if _, ok := checker.(*sourceBindingChecker); !ok {
		t.<PERSON><PERSON><PERSON>("NewSourceBindingChecker should return a *sourceBindingChecker instance")
	}
}

// TestSourceBindingChecker_IsMySQLTypeDatasource 测试MySQL类型数据源判断
func TestSourceBindingChecker_IsMySQLTypeDatasource(t *testing.T) {
	s := &sourceBindingChecker{}

	testCases := []struct {
		dbType   string
		expected bool
	}{
		{"mysql", true},
		{"MySQL", true},
		{"MYSQL", true},
		{"tidb", true},
		{"TiDB", true},
		{"TIDB", true},
		{"tidb-proxy", true},
		{"TiDB-Proxy", true},
		{"TIDB-PROXY", true},
		{"oracle", false},
		{"postgresql", false},
		{"mongodb", false},
		{"", false},
	}

	for _, tc := range testCases {
		result := s.isMySQLTypeDatasource(tc.dbType)
		if result != tc.expected {
			t.Errorf("isMySQLTypeDatasource(%s) = %v, expected %v", tc.dbType, result, tc.expected)
		}
	}
}

// TestSourceBindingLogic 测试数据源绑定逻辑
func TestSourceBindingLogic(t *testing.T) {
	s := &sourceBindingChecker{}

	// 创建测试数据源
	testDS := &struct {
		DatasourceId   int
		DatasourceName string
		DbType         string
	}{
		DatasourceId:   1,
		DatasourceName: "test-mysql-source",
		DbType:         "mysql",
	}

	// 测试数据源基本属性
	t.Run("Basic datasource properties", func(t *testing.T) {
		if testDS.DatasourceName == "" {
			t.Error("Test datasource name should not be empty")
		}
		
		if !s.isMySQLTypeDatasource(testDS.DbType) {
			t.Error("Test datasource should be MySQL type")
		}
	})

	// 测试绑定状态分析逻辑
	t.Run("Binding status analysis", func(t *testing.T) {
		// 模拟worker绑定映射
		workerBindingMap := map[string]interface{}{
			"test-mysql-source": struct {
				Name string
				Addr string
			}{
				Name: "dm-worker-01",
				Addr: "192.168.1.100:8262",
			},
		}
		
		if _, exists := workerBindingMap[testDS.DatasourceName]; !exists {
			t.Error("Test datasource should exist in binding map")
		}
		
		// 验证映射包含预期的源
		t.Logf("Worker binding map contains source: %s", testDS.DatasourceName)
	})
}

// TestIdleWorkerLogic 测试空闲Worker逻辑  
func TestIdleWorkerLogic(t *testing.T) {
	// 模拟worker列表
	workers := []*struct {
		Name   string
		Addr   string  
		Stage  string
		Source string
	}{
		{Name: "worker-01", Addr: "192.168.1.100:8262", Stage: "bound", Source: "mysql-source-1"},
		{Name: "worker-02", Addr: "192.168.1.101:8262", Stage: "idle", Source: ""},
		{Name: "worker-03", Addr: "192.168.1.102:8262", Stage: "free", Source: ""},
	}

	// 计算期望的空闲worker数量
	expectedIdleCount := 0
	for _, worker := range workers {
		if worker.Stage != "bound" {
			expectedIdleCount++
		}
	}

	if expectedIdleCount != 2 {
		t.Errorf("Expected 2 idle workers, got %d", expectedIdleCount)
	}
	
	t.Logf("Found %d idle workers out of %d total workers", expectedIdleCount, len(workers))
}
