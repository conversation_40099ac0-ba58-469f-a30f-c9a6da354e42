package checker

import (
	"context"
	"fmt"
	"strings"

	"gitee.com/pingcap_enterprise/tms/pkg/mysql/core"
	"gitee.com/pingcap_enterprise/tms/pkg/mysql/dm"
	"gitee.com/pingcap_enterprise/tms/server/message"
	"gitee.com/pingcap_enterprise/tms/server/models"
	"gitee.com/pingcap_enterprise/tms/server/models/datasource"
	"gitee.com/pingcap_enterprise/tms/util/log"
)

// sourceBindingChecker 数据源绑定关系检查器
type sourceBindingChecker struct{}

// SourceBindingDetail 数据源绑定详情
type SourceBindingDetail struct {
	SourceName    string // 数据源名称
	DatasourceId  int    // TMS数据源ID
	DbType        string // 数据库类型
	BindingStatus string // bound/unbound/error
	WorkerName    string // 绑定的Worker名称
	WorkerAddr    string // Worker地址
	Message       string // 状态描述
}

// NewSourceBindingChecker 创建数据源绑定关系检查器实例
func NewSourceBindingChecker() SourceBindingChecker {
	return &sourceBindingChecker{}
}

// Check 检查数据源绑定关系
func (s *sourceBindingChecker) Check(ctx context.Context, channelId int, masterAddr string, progress *core.CheckProgress, checkType core.CheckType) {
	log.Infof("SourceBindingChecker: Starting source binding relationship check for channel: %d, masterAddr: %s", channelId, masterAddr)

	progress.UpdateCheckItemByType(checkType, "running", 0)

	// 获取DM集群成员信息
	dmMembers, err := s.getDMMembers(ctx, masterAddr)
	if err != nil {
		log.Errorf("SourceBindingChecker: Failed to get DM cluster members, masterAddr: %s, error: %v", masterAddr, err)
		progress.UpdateCheckItemErrorByType(checkType, "Failed to get DM cluster members", err.Error(), "Please check DM cluster connection and ensure master address is correct")
		return
	}

	progress.UpdateCheckItemByType(checkType, "running", 20)

	// 获取通道关联的数据源信息
	channelDatasources, err := s.getChannelDatasources(ctx, channelId)
	if err != nil {
		log.Errorf("SourceBindingChecker: Failed to get channel datasources, channelId: %d, error: %v", channelId, err)
		progress.UpdateCheckItemErrorByType(checkType, "Failed to get channel datasources", err.Error(), "Please check channel configuration")
		return
	}

	progress.UpdateCheckItemByType(checkType, "running", 40)

	// 分析每个数据源的绑定状态
	sourceDetails := s.analyzeAllSourceBindings(channelDatasources, dmMembers.Workers)

	// 统计空闲worker数量
	idleWorkers := s.getIdleWorkers(dmMembers.Workers)

	progress.UpdateCheckItemByType(checkType, "running", 80)

	// 生成完整的检查结果
	s.generateDetailedResults(progress, checkType, sourceDetails, idleWorkers, dmMembers.Workers)

	log.Infof("SourceBindingChecker: Source binding relationship check completed for channel: %d, masterAddr: %s", channelId, masterAddr)
}

// getDMMembers 获取DM集群成员信息
func (s *sourceBindingChecker) getDMMembers(ctx context.Context, masterAddr string) (*message.ListMembersResp, error) {
	dmAdaptor, err := dm.NewDMAdaptor(masterAddr)
	if err != nil {
		return nil, fmt.Errorf("failed to create DM adaptor: %w", err)
	}

	members, err := dmAdaptor.ListMember(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to list DM cluster members: %w", err)
	}

	// 转换为message格式
	resp := &message.ListMembersResp{
		Leader:  &message.Leader{Message: members.Leader.Message, Name: members.Leader.Name, Addr: members.Leader.Addr},
		Masters: make([]*message.Master, len(members.Masters)),
		Workers: make([]*message.Worker, len(members.Workers)),
		Msg:     members.Msg,
		Result:  members.Result,
	}

	for i, master := range members.Masters {
		resp.Masters[i] = &message.Master{
			Name: master.Name, MemberID: master.MemberID,
			Alive: master.Alive, PeerURLs: master.PeerURLs, ClientURLs: master.ClientURLs,
		}
	}

	for i, worker := range members.Workers {
		resp.Workers[i] = &message.Worker{
			Name: worker.Name, Addr: worker.Addr, Stage: worker.Stage, Source: worker.Source,
		}
	}

	log.Infof("SourceBindingChecker: Retrieved DM cluster members - Leader: %s, Masters: %d, Workers: %d",
		resp.Leader.Name, len(resp.Masters), len(resp.Workers))

	return resp, nil
}

// getChannelDatasources 获取通道关联的数据源信息
func (s *sourceBindingChecker) getChannelDatasources(ctx context.Context, channelId int) ([]*datasource.Datasource, error) {
	// 获取通道关联的数据源
	channelDatasources, err := models.GetChannelReaderWriter().GetChannelDatasourcesByChannelId(ctx, channelId)
	if err != nil {
		return nil, fmt.Errorf("failed to get channel datasources: %w", err)
	}

	var datasources []*datasource.Datasource
	for _, channelDs := range channelDatasources {
		ds, err := models.GetDatasourceReaderWriter().Get(ctx, channelDs.DataSourceId)
		if err != nil {
			log.Warnf("SourceBindingChecker: Failed to get datasource details, datasourceId: %d, error: %v", channelDs.DataSourceId, err)
			continue
		}

		// 只处理MySQL/TiDB类型的数据源（增量同步需要）
		if s.isMySQLTypeDatasource(ds.DbType) {
			datasources = append(datasources, ds)
			log.Debugf("SourceBindingChecker: Added datasource %s (%s) for binding check", ds.DatasourceName, ds.DbType)
		} else {
			log.Debugf("SourceBindingChecker: Skipping non-MySQL datasource %s (%s)", ds.DatasourceName, ds.DbType)
		}
	}

	log.Infof("SourceBindingChecker: Found %d MySQL/TiDB datasources for channel %d", len(datasources), channelId)
	return datasources, nil
}

// isMySQLTypeDatasource 判断是否为MySQL类型的数据源
func (s *sourceBindingChecker) isMySQLTypeDatasource(dbType string) bool {
	mysqlTypes := []string{"mysql", "tidb", "tidb-proxy"}
	dbTypeLower := strings.ToLower(dbType)
	for _, mysqlType := range mysqlTypes {
		if dbTypeLower == mysqlType {
			return true
		}
	}
	return false
}

// analyzeAllSourceBindings 分析所有数据源的绑定状态
func (s *sourceBindingChecker) analyzeAllSourceBindings(datasources []*datasource.Datasource, workers []*message.Worker) []SourceBindingDetail {
	sourceDetails := make([]SourceBindingDetail, 0, len(datasources))

	// 创建worker绑定映射，key为source名称，value为worker信息
	workerBindingMap := make(map[string]*message.Worker)
	for _, worker := range workers {
		if worker.Stage == "bound" && worker.Source != "" {
			workerBindingMap[worker.Source] = worker
		}
	}

	// 分析每个数据源的绑定状态
	for _, ds := range datasources {
		detail := s.analyzeSourceBinding(ds, workerBindingMap)
		sourceDetails = append(sourceDetails, detail)
		log.Debugf("SourceBindingChecker: Analyzed source %s - status: %s, worker: %s",
			detail.SourceName, detail.BindingStatus, detail.WorkerName)
	}

	return sourceDetails
}

// analyzeSourceBinding 分析单个数据源的绑定状态
func (s *sourceBindingChecker) analyzeSourceBinding(ds *datasource.Datasource, workerBindingMap map[string]*message.Worker) SourceBindingDetail {
	detail := SourceBindingDetail{
		SourceName:    ds.DatasourceName,
		DatasourceId:  ds.DatasourceId,
		DbType:        strings.ToUpper(ds.DbType),
		BindingStatus: "unbound",
		WorkerName:    "",
		WorkerAddr:    "",
		Message:       "Not bound to any worker",
	}

	// 检查是否有worker绑定此数据源
	// DM source名称就是数据源名称
	if worker, exists := workerBindingMap[ds.DatasourceName]; exists {
		detail.BindingStatus = "bound"
		detail.WorkerName = worker.Name
		detail.WorkerAddr = worker.Addr
		detail.Message = fmt.Sprintf("Bound to worker %s at %s", worker.Name, worker.Addr)
	}

	return detail
}

// getIdleWorkers 获取空闲的worker列表
func (s *sourceBindingChecker) getIdleWorkers(workers []*message.Worker) []*message.Worker {
	var idleWorkers []*message.Worker
	for _, worker := range workers {
		if worker.Stage != "bound" {
			idleWorkers = append(idleWorkers, worker)
			log.Debugf("SourceBindingChecker: Found idle worker %s at %s", worker.Name, worker.Addr)
		}
	}
	return idleWorkers
}

// generateDetailedResults 生成详细的检查结果
func (s *sourceBindingChecker) generateDetailedResults(progress *core.CheckProgress, checkType core.CheckType,
	sourceDetails []SourceBindingDetail, idleWorkers []*message.Worker, allWorkers []*message.Worker) {

	var boundCount, unboundCount int
	var unboundSources []string

	// 创建Worker绑定详情列表
	bindingDetails := make([]message.WorkerBindingDetail, len(sourceDetails))

	// 1. 展示每个数据源的详细信息
	for i, detail := range sourceDetails {
		bindingDetails[i] = message.WorkerBindingDetail{
			SourceName:    detail.SourceName,
			DatasourceId:  detail.DatasourceId,
			DbType:        detail.DbType,
			BindingStatus: detail.BindingStatus,
			WorkerName:    detail.WorkerName,
			WorkerAddr:    detail.WorkerAddr,
			Message:       detail.Message,
		}

		if detail.BindingStatus == "bound" {
			boundCount++
			progress.AddCheckItemDetailByType(checkType, "success",
				fmt.Sprintf("Source '%s' (%s) is bound to worker '%s'",
					detail.SourceName, detail.DbType, detail.WorkerName),
				message.WorkerBindingInfo{
					WorkerName: detail.WorkerName,
					SourceName: detail.SourceName,
					Status:     "bound",
				}, "")
		} else {
			unboundCount++
			unboundSources = append(unboundSources, fmt.Sprintf("%s (%s)", detail.SourceName, detail.DbType))
			progress.AddCheckItemDetailByType(checkType, "info",
				fmt.Sprintf("Source '%s' (%s) is not bound to any worker",
					detail.SourceName, detail.DbType),
				message.WorkerBindingInfo{
					WorkerName: "",
					SourceName: detail.SourceName,
					Status:     "unbound",
				}, "")
		}
	}

	// 2. 展示空闲Worker信息
	for _, worker := range idleWorkers {
		progress.AddCheckItemDetailByType(checkType, "info",
			fmt.Sprintf("Worker '%s' at %s is idle and available for binding",
				worker.Name, worker.Addr),
			message.WorkerBindingInfo{
				WorkerName: worker.Name,
				SourceName: "",
				Status:     "idle",
			}, "")
	}

	// 3. 生成整体统计和建议
	idleWorkerCount := len(idleWorkers)
	totalWorkers := len(allWorkers)

	log.Infof("SourceBindingChecker: Summary - %d sources (%d bound, %d unbound), %d workers (%d bound, %d idle)",
		len(sourceDetails), boundCount, unboundCount, totalWorkers, totalWorkers-idleWorkerCount, idleWorkerCount)

	// 4. 根据绑定状态生成整体结果
	if len(sourceDetails) == 0 {
		progress.UpdateCheckItemByType(checkType, "success", 100)
		progress.AddCheckItemDetailByType(checkType, "info",
			"No MySQL/TiDB datasources found in this channel",
			message.WorkerBindingInfo{
				WorkerName: "N/A",
				SourceName: "No sources",
				Status:     "empty",
			}, "This channel does not require DM worker binding for MySQL/TiDB datasources")
	} else if unboundCount == 0 {
		// 所有数据源都已绑定
		progress.UpdateCheckItemByType(checkType, "success", 100)
		progress.AddCheckItemDetailByType(checkType, "success",
			fmt.Sprintf("All %d sources are properly bound to workers", boundCount),
			message.WorkerBindingInfo{
				WorkerName: fmt.Sprintf("%d bound workers", boundCount),
				SourceName: fmt.Sprintf("%d sources", boundCount),
				Status:     "fully_bound",
			}, "All datasources are ready for incremental synchronization")
	} else if idleWorkerCount >= unboundCount {
		// 有未绑定的数据源，但空闲worker足够
		progress.UpdateCheckItemByType(checkType, "success", 100)
		progress.AddCheckItemDetailByType(checkType, "warn",
			fmt.Sprintf("%d sources are unbound, but %d idle workers are available", unboundCount, idleWorkerCount),
			message.WorkerBindingInfo{
				WorkerName: fmt.Sprintf("%d idle workers", idleWorkerCount),
				SourceName: fmt.Sprintf("%d unbound sources", unboundCount),
				Status:     "sufficient_capacity",
			},
			fmt.Sprintf("Unbound sources: %s. Create DM sources to bind them to available workers.",
				strings.Join(unboundSources, ", ")))
	} else {
		// 空闲worker不足
		shortage := unboundCount - idleWorkerCount
		progress.UpdateCheckItemByType(checkType, "warn", 100)
		progress.AddCheckItemDetailByType(checkType, "error",
			fmt.Sprintf("Insufficient worker capacity: need %d more workers for %d unbound sources", shortage, unboundCount),
			message.WorkerBindingInfo{
				WorkerName: fmt.Sprintf("%d idle workers (need %d more)", idleWorkerCount, shortage),
				SourceName: fmt.Sprintf("%d unbound sources", unboundCount),
				Status:     "insufficient_capacity",
			},
			fmt.Sprintf("Unbound sources: %s. Please add %d more workers to the DM cluster or remove unused datasources from the channel.",
				strings.Join(unboundSources, ", "), shortage))
	}

	// 5. 添加集群概览信息
	progress.AddCheckItemDetailByType(checkType, "info",
		fmt.Sprintf("DM Cluster Overview: %d total workers (%d bound, %d idle) serving %d sources",
			totalWorkers, totalWorkers-idleWorkerCount, idleWorkerCount, len(sourceDetails)),
		message.WorkerBindingInfo{
			WorkerName: fmt.Sprintf("%d workers", totalWorkers),
			SourceName: fmt.Sprintf("%d sources", len(sourceDetails)),
			Status:     "cluster_summary",
		}, "")
}

// determineClusterHealth 确定集群健康状态
func (s *sourceBindingChecker) determineClusterHealth(unboundCount, idleWorkerCount int) string {
	if unboundCount == 0 {
		return "healthy"
	} else if idleWorkerCount >= unboundCount {
		return "warning"
	} else {
		return "critical"
	}
}
