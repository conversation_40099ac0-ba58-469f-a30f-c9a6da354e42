package preview

import (
	"context"
	"fmt"

	"gitee.com/pingcap_enterprise/tms/common/constants"
	"gitee.com/pingcap_enterprise/tms/pkg/mysql/core"
	"gitee.com/pingcap_enterprise/tms/server/message"
	"gitee.com/pingcap_enterprise/tms/server/models"
	"gitee.com/pingcap_enterprise/tms/server/models/channel"
	"gitee.com/pingcap_enterprise/tms/server/models/datasource"
	"gitee.com/pingcap_enterprise/tms/util/log"
	"gopkg.in/yaml.v3"
)

// configPreviewer 配置预览器
type configPreviewer struct {
	converter     core.DMConverter
	configBuilder core.TaskConfigBuilder
	treeBuilder   *TreeBuilder
}

// TreeBuilder 树构建器
type TreeBuilder struct{}

// NewTreeBuilder 创建树构建器实例
func NewTreeBuilder() *TreeBuilder {
	return &TreeBuilder{}
}

// NewConfigPreviewer 创建配置预览器实例
func NewConfigPreviewer(converter core.DMConverter, configBuilder core.TaskConfigBuilder) core.ConfigPreviewer {
	return &configPreviewer{
		converter:     converter,
		configBuilder: configBuilder,
		treeBuilder:   NewTreeBuilder(),
	}
}

// PreviewSources 预览数据源配置
func (p *configPreviewer) PreviewSources(ctx context.Context, channelId int) (*message.PreviewSourcesResp, error) {
	log.Infof("ConfigPreviewer: Starting to preview data source configurations for channel: %d", channelId)

	// 获取通道关联的数据源
	channelDatasources, err := models.GetChannelReaderWriter().GetChannelDatasourcesByChannelId(ctx, channelId)
	if err != nil {
		log.Errorf("ConfigPreviewer: Failed to get channel datasources, channelId: %d, error: %v", channelId, err)
		return nil, err
	}

	log.Infof("ConfigPreviewer: Found %d datasources for channel: %d", len(channelDatasources), channelId)

	// 转换为DM格式配置
	configs := make([]message.CustomSourceConfigDTO, 0, len(channelDatasources))

	for _, channelDs := range channelDatasources {
		// 获取数据源详细信息
		ds, err := models.GetDatasourceReaderWriter().Get(ctx, channelDs.DataSourceId)
		if err != nil {
			log.Errorf("ConfigPreviewer: Failed to get datasource details, channelId: %d, datasourceId: %d, error: %v", channelId, channelDs.DataSourceId, err)
			continue // 跳过此数据源，继续处理其他数据源
		}

		// 只处理MySQL/TiDB数据源
		if !p.isSupportedDatabaseType(ds.DbType) {
			log.Debugf("ConfigPreviewer: Skipping non-MySQL datasource %s, channelId: %d, type: %s", ds.DatasourceName, channelId, ds.DbType)
			continue
		}

		// 构建DM源配置
		config := p.buildSourceConfig(ds)
		configs = append(configs, config)

		log.Debugf("ConfigPreviewer: Converted datasource %s to DM format, channelId: %d", ds.DatasourceName, channelId)
	}

	log.Infof("ConfigPreviewer: Successfully converted %d datasources to DM format, channelId: %d", len(configs), channelId)

	return &message.PreviewSourcesResp{
		Configs: configs,
	}, nil
}

// PreviewConfiguration 预览任务配置
func (p *configPreviewer) PreviewConfiguration(ctx context.Context, channelId int, masterAddr string) (*message.PreviewConfigurationResp, error) {
	log.Infof("ConfigPreviewer: Starting to preview task configuration for channel: %d, masterAddr: %s", channelId, masterAddr)

	// 获取通道信息
	channelInfo, err := models.GetChannelReaderWriter().GetChannel(ctx, channelId)
	if err != nil {
		log.Errorf("ConfigPreviewer: Failed to get channel information, channelId: %d, error: %v", channelId, err)
		return nil, err
	}

	// 获取目标数据源
	targetDatasource, err := models.GetDatasourceReaderWriter().Get(ctx, channelInfo.DatasourceIdT)
	if err != nil {
		log.Errorf("ConfigPreviewer: Failed to get target datasource, channelId: %d, datasourceIdT: %d, error: %v", channelId, channelInfo.DatasourceIdT, err)
		return nil, err
	}

	// 获取源数据源
	channelDatasources, err := models.GetChannelReaderWriter().GetChannelDatasourcesByChannelId(ctx, channelId)
	if err != nil {
		log.Errorf("ConfigPreviewer: Failed to get channel datasources, channelId: %d, error: %v", channelId, err)
		return nil, err
	}

	sourceDatasources := make([]*datasource.Datasource, 0)
	for _, cd := range channelDatasources {
		ds, err := models.GetDatasourceReaderWriter().Get(ctx, cd.DataSourceId)
		if err != nil {
			continue
		}
		// 只包含MySQL/TiDB数据源
		if ds.DbType == constants.DB_TYPE_MYSQL || ds.DbType == constants.DB_TYPE_TIDB {
			sourceDatasources = append(sourceDatasources, ds)
		}
	}

	// 获取schema表
	schemaTables, err := models.GetChannelReaderWriter().GetChannelSchemaTablesByChannelId(ctx, channelId)
	if err != nil {
		log.Warnf("ConfigPreviewer: Failed to get schema tables, channelId: %d, error: %v", channelId, err)
		schemaTables = nil
	}

	// 过滤有效的schema tables
	var filteredSchemaTables []*channel.ChannelSchemaTable
	for _, schemaTable := range schemaTables {
		if schemaTable.TaskId == 0 {
			continue
		}
		filteredSchemaTables = append(filteredSchemaTables, schemaTable)
	}

	// 获取通道schemas
	channelSchemas, err := models.GetChannelReaderWriter().GetChannelSchemas(ctx, channelId)
	if err != nil {
		log.Warnf("ConfigPreviewer: Failed to get channel schemas, channelId: %d, error: %v", channelId, err)
		channelSchemas = nil
	}

	// 使用配置构建器构建任务配置
	taskConfig, err := p.configBuilder.BuildTaskConfig(
		channelInfo,
		targetDatasource,
		sourceDatasources,
		filteredSchemaTables,
		channelSchemas,
	)
	if err != nil {
		log.Errorf("ConfigPreviewer: Failed to build task configuration, channelId: %d, error: %v", channelId, err)
		return nil, err
	}

	log.Infof("ConfigPreviewer: Successfully built task configuration, channelId: %d, taskName: %s", channelId, taskConfig.Name)

	return &message.PreviewConfigurationResp{
		Config: *taskConfig,
	}, nil
}

// PreviewAll 预览所有配置
func (p *configPreviewer) PreviewAll(ctx context.Context, channelId int, masterAddr string) (*message.PreviewAllResp, error) {
	log.Infof("ConfigPreviewer: Starting to preview all configurations for channel: %d, masterAddr: %s", channelId, masterAddr)

	// 获取源配置
	sourcesResp, err := p.PreviewSources(ctx, channelId)
	if err != nil {
		log.Errorf("ConfigPreviewer: Failed to preview data source configurations, channelId: %d, error: %v", channelId, err)
		sourcesResp = &message.PreviewSourcesResp{Configs: []message.CustomSourceConfigDTO{}}
	}

	// 获取任务配置
	configResp, err := p.PreviewConfiguration(ctx, channelId, masterAddr)
	if err != nil {
		log.Errorf("ConfigPreviewer: Failed to preview task configuration, channelId: %d, masterAddr: %s, error: %v", channelId, masterAddr, err)
		configResp = nil
	}

	// 构建树结构
	tree := p.treeBuilder.BuildConfigTree(sourcesResp.Configs, configResp)

	log.Infof("ConfigPreviewer: Successfully built configuration tree, channelId: %d, masterAddr: %s, source configs: %d", channelId, masterAddr, len(sourcesResp.Configs))

	return &message.PreviewAllResp{
		Tree: tree,
	}, nil
}

// isSupportedDatabaseType 检查是否为支持的数据库类型
func (p *configPreviewer) isSupportedDatabaseType(dbType string) bool {
	return dbType == constants.DB_TYPE_MYSQL || dbType == constants.DB_TYPE_TIDB
}

// buildSourceConfig 构建DM源配置
func (p *configPreviewer) buildSourceConfig(ds *datasource.Datasource) message.CustomSourceConfigDTO {
	return message.CustomSourceConfigDTO{
		SourceID:        ds.DatasourceName, // 使用数据源名称作为source-id
		EnableGTID:      false,             // 默认为false
		EnableRelay:     false,             // 默认为false
		RelayBinlogName: "",                // 默认为空
		RelayBinlogGTID: "",                // 默认为空
		From: message.CustomDatabaseConfig{
			Host:     ds.HostIp,
			Port:     ds.HostPort,
			User:     ds.UserName,
			Password: ds.PasswordValue, // 使用明文密码
			Security: nil,              // SSL配置在datasource模型中不可用
		},
	}
}

// BuildConfigTree 构建配置树
func (tb *TreeBuilder) BuildConfigTree(sourceConfigs []message.CustomSourceConfigDTO, taskConfig *message.PreviewConfigurationResp) *message.DMConfigTreeNode {
	// 创建根节点
	rootNode := &message.DMConfigTreeNode{
		Key:      "dm-migration-root",
		Title:    "DM Migration Configuration",
		IsLeaf:   false,
		Children: make([]*message.DMConfigTreeNode, 0),
	}

	// 创建数据源节点
	sourcesNode := &message.DMConfigTreeNode{
		Key:      "sources-root",
		Title:    "数据源配置",
		IsLeaf:   false,
		Children: make([]*message.DMConfigTreeNode, 0),
	}

	// 添加源配置到树
	for i, sourceConfig := range sourceConfigs {
		sourceYAML, err := toYAML(sourceConfig)
		if err != nil {
			log.Warnf("TreeBuilder: Failed to convert source config to YAML, source: %s, error: %v", sourceConfig.SourceID, err)
			sourceYAML = fmt.Sprintf("# Error converting to YAML: %v", err)
		}

		sourceNode := &message.DMConfigTreeNode{
			Key:     fmt.Sprintf("source_%s_%d", sourceConfig.SourceID, i),
			Title:   fmt.Sprintf("source_%s.yaml", sourceConfig.SourceID),
			Context: sourceYAML,
			IsLeaf:  true,
		}
		sourcesNode.Children = append(sourcesNode.Children, sourceNode)
	}

	// 添加数据源节点到根节点
	rootNode.Children = append(rootNode.Children, sourcesNode)

	// 创建任务配置节点
	taskNode := &message.DMConfigTreeNode{
		Key:      "task-root",
		Title:    "Task Configuration",
		IsLeaf:   false,
		Children: make([]*message.DMConfigTreeNode, 0),
	}

	// 添加任务配置
	if taskConfig != nil {
		taskYAML, err := toYAML(taskConfig.Config)
		if err != nil {
			log.Warnf("TreeBuilder: Failed to convert task config to YAML, taskName: %s, error: %v", taskConfig.Config.Name, err)
			taskYAML = fmt.Sprintf("# Error converting to YAML: %v", err)
		}

		taskConfigNode := &message.DMConfigTreeNode{
			Key:     fmt.Sprintf("task_%s", taskConfig.Config.Name),
			Title:   fmt.Sprintf("task_%s.yaml", taskConfig.Config.Name),
			Context: taskYAML,
			IsLeaf:  true,
		}
		taskNode.Children = append(taskNode.Children, taskConfigNode)
	}

	// 添加任务节点到根节点
	rootNode.Children = append(rootNode.Children, taskNode)

	return rootNode
}

// toYAML 将结构体转换为YAML字符串
func toYAML(v interface{}) (string, error) {
	data, err := yaml.Marshal(v)
	if err != nil {
		return "", err
	}
	return string(data), nil
}
