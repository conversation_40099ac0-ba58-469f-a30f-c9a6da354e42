package config

import (
	"fmt"

	"gitee.com/pingcap_enterprise/tms/common/constants"
	"gitee.com/pingcap_enterprise/tms/common/errors"
	"gitee.com/pingcap_enterprise/tms/pkg/mysql/core"
	"gitee.com/pingcap_enterprise/tms/server/message"
	"gitee.com/pingcap_enterprise/tms/server/models/channel"
	"gitee.com/pingcap_enterprise/tms/server/models/datasource"
	"gitee.com/pingcap_enterprise/tms/util/log"
)

// taskConfigBuilder 实现任务配置构建接口
type taskConfigBuilder struct {
	converter core.DMConverter
}

// NewTaskConfigBuilder 创建任务配置构建器实例
func NewTaskConfigBuilder(converter core.DMConverter) core.TaskConfigBuilder {
	return &taskConfigBuilder{
		converter: converter,
	}
}

// BuildTaskConfig 构建任务配置
func (b *taskConfigBuilder) BuildTaskConfig(
	channelInfo *channel.ChannelInformation,
	targetDatasource *datasource.Datasource,
	sourceDatasources []*datasource.Datasource,
	schemaTables []*channel.ChannelSchemaTable,
	channelSchemas []*channel.ChannelSchema,
) (*message.TaskConfigDTO, error) {
	log.Infof("TaskConfigBuilder: Starting to build task configuration for channel: %s", channelInfo.ChannelName)

	// 参数验证
	if err := b.validateInput(channelInfo, targetDatasource, sourceDatasources); err != nil {
		return nil, err
	}

	// 初始化配置结构
	config := b.initializeConfig()

	// 构建基础配置
	if err := b.assembleBasicConfig(config, channelInfo, targetDatasource); err != nil {
		log.Errorf("TaskConfigBuilder: Failed to assemble basic configuration for channel: %s, error: %v", channelInfo.ChannelName, err)
		return nil, err
	}

	// 构建规则配置（路由、过滤器、黑白名单）
	if err := b.assembleRulesConfig(config, schemaTables, channelSchemas); err != nil {
		log.Errorf("TaskConfigBuilder: Failed to assemble rules configuration for channel: %s, error: %v", channelInfo.ChannelName, err)
		return nil, err
	}

	// 构建处理器配置
	if err := b.assembleProcessorConfig(config); err != nil {
		log.Errorf("TaskConfigBuilder: Failed to assemble processor configuration for channel: %s, error: %v", channelInfo.ChannelName, err)
		return nil, err
	}

	// 构建MySQL实例配置
	if err := b.assembleMySQLInstances(config, sourceDatasources, config.TaskMode); err != nil {
		log.Errorf("TaskConfigBuilder: Failed to assemble MySQL instances configuration for channel: %s, error: %v", channelInfo.ChannelName, err)
		return nil, err
	}

	log.Infof("TaskConfigBuilder: Successfully built task configuration for channel: %s, task name: %s", channelInfo.ChannelName, config.Name)
	return config, nil
}

// validateInput 验证输入参数
func (b *taskConfigBuilder) validateInput(
	channelInfo *channel.ChannelInformation,
	targetDatasource *datasource.Datasource,
	sourceDatasources []*datasource.Datasource,
) error {
	if channelInfo == nil {
		return errors.NewError(errors.TIMS_PARAMETER_INVALID, "通道信息不能为空")
	}
	if targetDatasource == nil {
		return errors.NewError(errors.TIMS_DM_TARGET_DB_NOT_FOUND, "目标数据源不能为空")
	}
	if len(sourceDatasources) == 0 {
		return errors.NewError(errors.TIMS_DM_SOURCE_DB_NOT_FOUND, "至少需要一个源数据源")
	}
	return nil
}

// initializeConfig 初始化配置结构
func (b *taskConfigBuilder) initializeConfig() *message.TaskConfigDTO {
	return &message.TaskConfigDTO{
		Routes:         make(map[string]*message.RouteRule),
		Filters:        make(map[string]*message.BinlogEventRule),
		BlockAllowList: make(map[string]*message.BlockAllowListRule),
		Mydumpers:      make(map[string]*message.MydumperConfig),
		Loaders:        make(map[string]*message.LoaderConfig),
		Syncers:        make(map[string]*message.SyncerConfig),
		Validators:     make(map[string]*message.ValidatorConfig),
	}
}

// assembleBasicConfig 组装基础任务配置
func (b *taskConfigBuilder) assembleBasicConfig(
	config *message.TaskConfigDTO,
	channelInfo *channel.ChannelInformation,
	targetDatasource *datasource.Datasource,
) error {
	log.Debugf("TaskConfigBuilder: Assembling basic configuration for channel: %s", channelInfo.ChannelName)

	// 设置任务名称
	config.Name = fmt.Sprintf("task_%s", channelInfo.ChannelName)

	// 根据通道设置确定任务模式
	if channelInfo.MigrateFullAndIncrementData == "Y" {
		config.TaskMode = "all" // 全量 + 增量
	} else if channelInfo.Increment == "Y" {
		config.TaskMode = "incremental" // 仅增量
	} else {
		config.TaskMode = "full" // 仅全量数据迁移
	}

	// 设置分片模式（默认：无分片）
	config.IsSharding = false
	config.ShardMode = ""
	config.StrictOptimisticShardMode = false

	// 设置元数据配置
	config.MetaSchema = "dm_meta"
	config.CaseSensitive = false
	config.CollationCompatible = "loose"

	// 设置在线DDL处理
	config.OnlineDDL = true
	config.OnlineDDLScheme = "gh-ost"

	// 设置其他基础配置
	config.CleanDumpFile = true
	config.EnableHeartbeat = false
	config.Timezone = ""

	// 验证目标数据库是MySQL/TiDB
	if targetDatasource.DbType != constants.DB_TYPE_MYSQL && targetDatasource.DbType != constants.DB_TYPE_TIDB {
		log.Errorf("TaskConfigBuilder: Target datasource is not MySQL/TiDB, channel: %s, type: %s", channelInfo.ChannelName, targetDatasource.DbType)
		return errors.NewError(errors.TIMS_DM_TARGET_DB_NOT_FOUND, "目标必须是MySQL或TiDB")
	}

	// 配置目标数据库
	config.TargetDatabase = &message.TaskTargetDatabase{
		Host:     targetDatasource.HostIp,
		Port:     targetDatasource.HostPort,
		User:     targetDatasource.UserName,
		Password: targetDatasource.PasswordValue,
		Session: map[string]string{
			"sql_mode":                "ANSI_QUOTES,NO_ZERO_IN_DATE,NO_ZERO_DATE",
			"tidb_skip_utf8_check":    "1",
			"sql_require_primary_key": "OFF",
		},
	}

	log.Debugf("TaskConfigBuilder: Basic configuration assembly completed for channel: %s", channelInfo.ChannelName)
	return nil
}

// assembleRulesConfig 组装路由、过滤器和黑白名单规则
func (b *taskConfigBuilder) assembleRulesConfig(
	config *message.TaskConfigDTO,
	schemaTables []*channel.ChannelSchemaTable,
	channelSchemas []*channel.ChannelSchema,
) error {
	log.Debugf("TaskConfigBuilder: Assembling rules configuration")

	// 1. 生成路由规则
	b.generateRouteRules(config, schemaTables, channelSchemas)

	// 2. 生成过滤器规则
	b.generateFilterRules(config)

	// 3. 生成黑白名单规则（每个表一个do-tables条目）
	b.generateBlockAllowListRules(config, schemaTables, channelSchemas)

	log.Debugf("TaskConfigBuilder: Rules configuration assembly completed")
	return nil
}

// generateRouteRules 生成优化的路由规则
func (b *taskConfigBuilder) generateRouteRules(
	config *message.TaskConfigDTO,
	schemaTables []*channel.ChannelSchemaTable,
	channelSchemas []*channel.ChannelSchema,
) {
	if len(schemaTables) > 0 {
		b.generateOptimizedTableRoutes(config, schemaTables)
	} else if len(channelSchemas) > 0 {
		b.generateSchemaOnlyRoutes(config, channelSchemas)
	} else {
		log.Warn("TaskConfigBuilder: No schema tables or schemas found, no routes generated")
	}
}

// generateOptimizedTableRoutes 生成带表重命名优化的路由
func (b *taskConfigBuilder) generateOptimizedTableRoutes(
	config *message.TaskConfigDTO,
	schemaTables []*channel.ChannelSchemaTable,
) {
	log.Debugf("TaskConfigBuilder: Generating optimized routes based on %d tables", len(schemaTables))

	routeIndex := 1
	for _, st := range schemaTables {
		routeKey := fmt.Sprintf("route_%d", routeIndex)

		config.Routes[routeKey] = &message.RouteRule{
			SchemaPattern: st.SchemaNameS,
			TablePattern:  st.TableNameS,
			TargetSchema:  st.SchemaNameT,
			TargetTable:   st.TableNameT,
		}

		routeIndex++
		log.Debugf("TaskConfigBuilder: Added route rule %s: %s.%s -> %s.%s",
			routeKey, st.SchemaNameS, st.TableNameS, st.SchemaNameT, st.TableNameT)
	}
}

// generateSchemaOnlyRoutes 生成仅基于schema的路由
func (b *taskConfigBuilder) generateSchemaOnlyRoutes(
	config *message.TaskConfigDTO,
	channelSchemas []*channel.ChannelSchema,
) {
	log.Debugf("TaskConfigBuilder: Generating routes based on %d schemas", len(channelSchemas))

	routeIndex := 1
	for _, cs := range channelSchemas {
		routeKey := fmt.Sprintf("schema_route_%d", routeIndex)

		config.Routes[routeKey] = &message.RouteRule{
			SchemaPattern: cs.SchemaNameS,
			TablePattern:  "*", // 匹配所有表
			TargetSchema:  cs.SchemaNameT,
			TargetTable:   "", // 保持原表名
		}

		routeIndex++
		log.Debugf("TaskConfigBuilder: Added schema route rule %s: %s.* -> %s.*",
			routeKey, cs.SchemaNameS, cs.SchemaNameT)
	}
}

// generateFilterRules 生成过滤器规则
func (b *taskConfigBuilder) generateFilterRules(config *message.TaskConfigDTO) {
	// 默认过滤器：忽略系统事件
	config.Filters["global"] = &message.BinlogEventRule{
		SchemaPattern: "*",
		TablePattern:  "*",
		Events:        []string{"truncate table", "drop database"},
		Action:        "Ignore",
	}
}

// generateBlockAllowListRules 生成黑白名单规则
func (b *taskConfigBuilder) generateBlockAllowListRules(
	config *message.TaskConfigDTO,
	schemaTables []*channel.ChannelSchemaTable,
	channelSchemas []*channel.ChannelSchema,
) {
	if len(schemaTables) > 0 {
		b.generateTableSpecificBlockAllowList(config, schemaTables)
	} else if len(channelSchemas) > 0 {
		b.generateSchemaBlockAllowList(config, channelSchemas)
	}
}

// generateTableSpecificBlockAllowList 生成表级别的黑白名单
func (b *taskConfigBuilder) generateTableSpecificBlockAllowList(
	config *message.TaskConfigDTO,
	schemaTables []*channel.ChannelSchemaTable,
) {
	// 按schema分组表
	schemaTableMap := make(map[string][]string)
	for _, st := range schemaTables {
		schemaTableMap[st.SchemaNameS] = append(schemaTableMap[st.SchemaNameS], st.TableNameS)
	}

	// 生成每个schema的do-tables规则
	ruleIndex := 1
	for schema, tables := range schemaTableMap {
		ruleKey := fmt.Sprintf("ba_list_%d", ruleIndex)

		// 为每个表创建TableRef
		tableRefs := make([]*message.TableRef, 0, len(tables))
		for _, table := range tables {
			tableRefs = append(tableRefs, &message.TableRef{
				DbName:  schema,
				TblName: table,
			})
		}

		config.BlockAllowList[ruleKey] = &message.BlockAllowListRule{
			DoDbs:    []string{schema},
			DoTables: tableRefs,
		}

		ruleIndex++
		log.Debugf("TaskConfigBuilder: Added block-allow list rule %s: schema=%s, tables=%v",
			ruleKey, schema, tables)
	}
}

// generateSchemaBlockAllowList 生成schema级别的黑白名单
func (b *taskConfigBuilder) generateSchemaBlockAllowList(
	config *message.TaskConfigDTO,
	channelSchemas []*channel.ChannelSchema,
) {
	// 收集所有源schema名称
	var doDbs []string
	for _, cs := range channelSchemas {
		doDbs = append(doDbs, cs.SchemaNameS)
	}

	if len(doDbs) > 0 {
		config.BlockAllowList["schema_filter"] = &message.BlockAllowListRule{
			DoDbs: doDbs,
		}
		log.Debugf("TaskConfigBuilder: Added schema block-allow list rule, schemas: %v", doDbs)
	}
}

// assembleProcessorConfig 组装mydumpers、loaders、syncers和validators配置
func (b *taskConfigBuilder) assembleProcessorConfig(config *message.TaskConfigDTO) error {
	log.Debugf("TaskConfigBuilder: Assembling processor configurations")

	// 配置mydumpers（用于全量数据导出）
	config.Mydumpers["global"] = &message.MydumperConfig{
		Threads:       4,
		ChunkFilesize: "64", // 64MB每个块
		ExtraArgs:     "--consistency auto",
	}

	// 配置loaders（用于全量数据导入）
	config.Loaders["global"] = &message.LoaderConfig{
		PoolSize:            16,
		Dir:                 "./dumped_data",
		ImportMode:          "logical",
		OnDuplicateLogical:  "replace",
		OnDuplicatePhysical: "none",
		ChecksumPhysical:    "required",
		Analyze:             "off",
	}

	// 配置syncers（用于增量复制）
	config.Syncers["global"] = &message.SyncerConfig{
		WorkerCount:      16,
		Batch:            100,
		SafeMode:         false,
		SafeModeDuration: "60s",
		Compact:          false,
		MultipleRows:     false,
		EnableAnsiQuotes: true,
	}

	// 配置validators（用于数据验证）
	config.Validators["global"] = &message.ValidatorConfig{
		Mode:          "none", // 默认禁用
		WorkerCount:   4,
		RowErrorDelay: "30m",
	}

	log.Debugf("TaskConfigBuilder: Processor configurations assembly completed")
	return nil
}

// assembleMySQLInstances 组装MySQL实例配置并引用规则
func (b *taskConfigBuilder) assembleMySQLInstances(
	config *message.TaskConfigDTO,
	sourceDatasources []*datasource.Datasource,
	taskMode string,
) error {
	log.Debugf("TaskConfigBuilder: Assembling MySQL instance configurations for %d source datasources", len(sourceDatasources))

	config.MySQLInstances = make([]*message.MySQLInstance, 0, len(sourceDatasources))

	// 收集所有规则名称用于引用
	var routeRules []string
	for name := range config.Routes {
		routeRules = append(routeRules, name)
	}

	var filterRules []string
	for name := range config.Filters {
		filterRules = append(filterRules, name)
	}

	var blockAllowRules []string
	for name := range config.BlockAllowList {
		blockAllowRules = append(blockAllowRules, name)
	}

	for _, ds := range sourceDatasources {
		// 仅处理MySQL/TiDB数据源
		if ds.DbType != constants.DB_TYPE_MYSQL && ds.DbType != constants.DB_TYPE_TIDB {
			log.Debugf("TaskConfigBuilder: Skipping non-MySQL datasource %s, type: %s", ds.DatasourceName, ds.DbType)
			continue
		}

		instance := &message.MySQLInstance{
			SourceID:            ds.DatasourceName,
			FilterRules:         filterRules,
			RouteRules:          routeRules,
			BlockAllowList:      "global", // 使用全局黑白名单
			MydumperConfigName:  "global",
			LoaderConfigName:    "global",
			SyncerConfigName:    "global",
			ValidatorConfigName: "global",
		}

		// 如果有多个黑白名单规则，选择第一个
		if len(blockAllowRules) > 0 {
			instance.BlockAllowList = blockAllowRules[0]
		}

		config.MySQLInstances = append(config.MySQLInstances, instance)
		log.Debugf("TaskConfigBuilder: Added MySQL instance configuration: %s", ds.DatasourceName)
	}

	log.Debugf("TaskConfigBuilder: MySQL instance configurations assembly completed, total %d instances", len(config.MySQLInstances))
	return nil
}
