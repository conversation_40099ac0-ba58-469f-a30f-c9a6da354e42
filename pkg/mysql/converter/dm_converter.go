package converter

import (
	"gitee.com/pingcap_enterprise/tms/pkg/mysql/core"
	"gitee.com/pingcap_enterprise/tms/pkg/mysql/dm"
	"gitee.com/pingcap_enterprise/tms/server/message"
)

// dmConverter 实现核心转换接口
type dmConverter struct{}

// NewDMConverter 创建DM转换器实例
func NewDMConverter() core.DMConverter {
	return &dmConverter{}
}

// ToCustomSourceConfig 转换DM源配置到自定义格式
func (c *dmConverter) ToCustomSourceConfig(dmConfig interface{}) *message.CustomSourceConfigDTO {
	config, ok := dmConfig.(*dm.SourceConfigDTO)
	if !ok || config == nil {
		return nil
	}

	return &message.CustomSourceConfigDTO{
		SourceID:        config.SourceID,
		EnableGTID:      config.EnableGTID,
		EnableRelay:     config.EnableRelay,
		RelayBinlogName: config.RelayBinlogName,
		RelayBinlogGTID: config.RelayBinlogGTID,
		RelayDir:        config.RelayDir,
		From:            c.ToCustomDatabaseConfig(config.From),
		Purge:           config.Purge,
		Checker:         config.Checker,
		CaseSensitive:   config.CaseSensitive,
		Filters:         config.Filters,
	}
}

// FromCustomSourceConfig 转换自定义格式到DM源配置
func (c *dmConverter) FromCustomSourceConfig(msgConfig *message.CustomSourceConfigDTO) interface{} {
	if msgConfig == nil {
		return nil
	}

	return &dm.SourceConfigDTO{
		SourceID:        msgConfig.SourceID,
		EnableGTID:      msgConfig.EnableGTID,
		EnableRelay:     msgConfig.EnableRelay,
		RelayBinlogName: msgConfig.RelayBinlogName,
		RelayBinlogGTID: msgConfig.RelayBinlogGTID,
		RelayDir:        msgConfig.RelayDir,
		From:            c.FromCustomDatabaseConfig(msgConfig.From).(dm.DatabaseConfig),
		Purge:           msgConfig.Purge,
		Checker:         msgConfig.Checker,
		CaseSensitive:   msgConfig.CaseSensitive,
		Filters:         msgConfig.Filters,
	}
}

// ToCustomDatabaseConfig 转换数据库配置
func (c *dmConverter) ToCustomDatabaseConfig(dmDBConfig interface{}) message.CustomDatabaseConfig {
	config, ok := dmDBConfig.(dm.DatabaseConfig)
	if !ok {
		return message.CustomDatabaseConfig{}
	}

	return message.CustomDatabaseConfig{
		Host:            config.Host,
		Port:            config.Port,
		User:            config.User,
		Password:        config.Password,
		PasswordEncrypt: "", // 将在fillSourcePasswords中填充
		Security:        config.Security,
	}
}

// FromCustomDatabaseConfig 转换自定义数据库配置
func (c *dmConverter) FromCustomDatabaseConfig(msgDBConfig message.CustomDatabaseConfig) interface{} {
	// 优先使用加密密码，否则使用明文密码
	password := msgDBConfig.Password
	if msgDBConfig.PasswordEncrypt != "" {
		// 在实际实现中，这里需要解密逻辑
		password = msgDBConfig.Password
	}

	return dm.DatabaseConfig{
		Host:     msgDBConfig.Host,
		Port:     msgDBConfig.Port,
		User:     msgDBConfig.User,
		Password: password,
		Security: msgDBConfig.Security,
	}
}

// 以下是从原dm_converter.go迁移的其他转换方法，保持向后兼容

// ToOperateSourceResponse 转换操作源响应
func (c *dmConverter) ToOperateSourceResponse(dmResp *dm.OperateSourceResponseDTO) *message.OperateSourceResponseDTO {
	if dmResp == nil {
		return nil
	}

	msgResp := &message.OperateSourceResponseDTO{
		Result:  dmResp.Result,
		Msg:     dmResp.Msg,
		Sources: make([]*message.CommonWorkerResponseDTO, len(dmResp.Sources)),
	}

	for i, source := range dmResp.Sources {
		msgResp.Sources[i] = c.ToCommonWorkerResponse(source)
	}

	return msgResp
}

// ToCommonWorkerResponse 转换通用Worker响应
func (c *dmConverter) ToCommonWorkerResponse(dmWorkerResp *dm.CommonWorkerResponseDTO) *message.CommonWorkerResponseDTO {
	if dmWorkerResp == nil {
		return nil
	}

	return &message.CommonWorkerResponseDTO{
		Result: dmWorkerResp.Result,
		Msg:    dmWorkerResp.Msg,
		Source: dmWorkerResp.Source,
		Worker: dmWorkerResp.Worker,
	}
}

// ToMeta 转换Meta配置
func (c *dmConverter) ToMeta(dmMeta *dm.Meta) *message.Meta {
	if dmMeta == nil {
		return nil
	}

	return &message.Meta{
		BinLogName: dmMeta.BinLogName,
		BinLogPos:  dmMeta.BinLogPos,
		BinLogGTID: dmMeta.BinLogGTID,
	}
}

// FromMeta 转换Meta配置
func (c *dmConverter) FromMeta(msgMeta *message.Meta) *dm.Meta {
	if msgMeta == nil {
		return nil
	}

	return &dm.Meta{
		BinLogName: msgMeta.BinLogName,
		BinLogPos:  msgMeta.BinLogPos,
		BinLogGTID: msgMeta.BinLogGTID,
	}
}
