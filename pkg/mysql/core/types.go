package core

import (
	"sync"
	"time"

	"gitee.com/pingcap_enterprise/tms/server/message"
)

// CheckType 检查类型枚举（用于替代基于索引的硬编码）
// 使用字符串以便于 JSON 序列化与向后兼容
// 注意：新增类型时需同步更新 GetAllCheckTypes 执行顺序
//
// English logs only, comments in Chinese for maintainability.
// 上述抑制用于兼容IDE检查，避免误报
//
//goland:noinspection GoUnusedConst
//goland:noinspection GoUnusedExportedFunction
//goland:noinspection GoUnusedParameter
//goland:noinspection GoUnnecessarilyExportedIdentifiers
const ()

type CheckType string

const (
	// 表结构一致性检查
	CheckTypeTableConsistency CheckType = "table_consistency"
	// 数据源绑定关系检查
	CheckTypeWorkerBinding CheckType = "worker_binding"
	// 数据源权限检查
	CheckTypeSourcePermission CheckType = "source_permission"
)

// GetCheckTypeName 根据检查类型返回默认展示名称
func GetCheckTypeName(checkType CheckType) string {
	switch checkType {
	case CheckTypeTableConsistency:
		return "Table Structure Consistency Check"
	case CheckTypeWorkerBinding:
		return "Source Binding Relationship Check"
	case CheckTypeSourcePermission:
		return "Data Source Permission Check"
	default:
		return string(checkType)
	}
}

// GetAllCheckTypes 返回全部检查类型（按执行顺序）
func GetAllCheckTypes() []CheckType {
	return []CheckType{
		CheckTypeTableConsistency,
		CheckTypeWorkerBinding,
		CheckTypeSourcePermission,
	}
}

// CheckProgress 检查进度跟踪结构体
type CheckProgress struct {
	RequestID       string              `json:"requestId"`
	OverallStatus   string              `json:"overallStatus"`
	OverallProgress int                 `json:"overallProgress"`
	CheckItems      []message.CheckItem `json:"checkItems"`
	StartTime       time.Time           `json:"startTime"`
	EndTime         *time.Time          `json:"endTime"`
	mu              sync.RWMutex        `json:"-"`
}

// Lock 获取写锁
func (cp *CheckProgress) Lock() {
	cp.mu.Lock()
}

// Unlock 释放写锁
func (cp *CheckProgress) Unlock() {
	cp.mu.Unlock()
}

// RLock 获取读锁
func (cp *CheckProgress) RLock() {
	cp.mu.RLock()
}

// RUnlock 释放读锁
func (cp *CheckProgress) RUnlock() {
	cp.mu.RUnlock()
}

// CheckRequest 检查请求参数
type CheckRequest struct {
	ChannelID  int
	MasterAddr string
	RequestID  string
}

// SourcePreviewRequest 数据源预览请求参数
type SourcePreviewRequest struct {
	ChannelID int
}

// ConfigPreviewRequest 配置预览请求参数
type ConfigPreviewRequest struct {
	ChannelID  int
	MasterAddr string
}

// GetCheckItemByType 根据检查类型获取检查项与其索引
func (cp *CheckProgress) GetCheckItemByType(checkType CheckType) (*message.CheckItem, int) {
	for i := range cp.CheckItems {
		if cp.CheckItems[i].CheckType == string(checkType) {
			return &cp.CheckItems[i], i
		}
	}
	return nil, -1
}

// UpdateCheckItemByType 按类型更新检查项状态与进度
func (cp *CheckProgress) UpdateCheckItemByType(checkType CheckType, status string, progress int) {
	cp.Lock()
	defer cp.Unlock()
	if item, idx := cp.GetCheckItemByType(checkType); item != nil {
		cp.CheckItems[idx].Status = status
		cp.CheckItems[idx].Progress = progress
	}
}

// UpdateCheckItemErrorByType 按类型设置失败状态与错误信息/建议
func (cp *CheckProgress) UpdateCheckItemErrorByType(checkType CheckType, msg, errorMsg, suggestion string) {
	cp.Lock()
	defer cp.Unlock()
	if item, idx := cp.GetCheckItemByType(checkType); item != nil {
		cp.CheckItems[idx].Status = "failed"
		cp.CheckItems[idx].Progress = 100
		cp.CheckItems[idx].ErrorMsg = errorMsg
		cp.CheckItems[idx].Suggestion = suggestion
		cp.CheckItems[idx].Details = append(cp.CheckItems[idx].Details, message.CheckItemDetail{
			Type:       "error",
			Message:    msg,
			Suggestion: suggestion,
		})
	}
}

// AddCheckItemDetailByType 按类型添加详情
func (cp *CheckProgress) AddCheckItemDetailByType(checkType CheckType, detailType, detailMessage string, data interface{}, suggestion string) {
	cp.Lock()
	defer cp.Unlock()
	if item, idx := cp.GetCheckItemByType(checkType); item != nil {
		cp.CheckItems[idx].Details = append(cp.CheckItems[idx].Details, message.CheckItemDetail{
			Type:       detailType,
			Message:    detailMessage,
			Data:       data,
			Suggestion: suggestion,
		})
	}
}
