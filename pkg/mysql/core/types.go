package core

import (
	"fmt"
	"sync"
	"time"

	"gitee.com/pingcap_enterprise/tms/server/message"
)

// CheckType 检查类型枚举（用于替代基于索引的硬编码）
// 使用字符串以便于 JSON 序列化与向后兼容
// 注意：新增类型时需同步更新 GetAllCheckTypes 执行顺序
//
// English logs only, comments in Chinese for maintainability.
// 上述抑制用于兼容IDE检查，避免误报
//
//goland:noinspection GoUnusedConst
//goland:noinspection GoUnusedExportedFunction
//goland:noinspection GoUnusedParameter
//goland:noinspection GoUnnecessarilyExportedIdentifiers
const ()

type CheckType string

const (
	// 表结构一致性检查
	CheckTypeTableConsistency CheckType = "table_consistency"
	// 数据源绑定关系检查
	CheckTypeWorkerBinding CheckType = "worker_binding"
	// 数据源权限检查
	CheckTypeSourcePermission CheckType = "source_permission"
)

// GetCheckTypeName 根据检查类型返回默认展示名称
func GetCheckTypeName(checkType CheckType) string {
	switch checkType {
	case CheckTypeTableConsistency:
		return "Table Structure Consistency Check"
	case CheckTypeWorkerBinding:
		return "Source Binding Relationship Check"
	case CheckTypeSourcePermission:
		return "Data Source Permission Check"
	default:
		return string(checkType)
	}
}

// GetAllCheckTypes 返回全部检查类型（按执行顺序）
func GetAllCheckTypes() []CheckType {
	return []CheckType{
		CheckTypeTableConsistency,
		CheckTypeWorkerBinding,
		CheckTypeSourcePermission,
	}
}

// CheckProgress 检查进度跟踪结构体
type CheckProgress struct {
	RequestID       string              `json:"requestId"`
	OverallStatus   string              `json:"overallStatus"`
	OverallProgress int                 `json:"overallProgress"`
	CheckItems      []message.CheckItem `json:"checkItems"`
	StartTime       time.Time           `json:"startTime"`
	EndTime         *time.Time          `json:"endTime"`
	mu              sync.RWMutex        `json:"-"`
}

// Lock 获取写锁
func (cp *CheckProgress) Lock() {
	cp.mu.Lock()
}

// Unlock 释放写锁
func (cp *CheckProgress) Unlock() {
	cp.mu.Unlock()
}

// RLock 获取读锁
func (cp *CheckProgress) RLock() {
	cp.mu.RLock()
}

// RUnlock 释放读锁
func (cp *CheckProgress) RUnlock() {
	cp.mu.RUnlock()
}

// CheckRequest 检查请求参数
type CheckRequest struct {
	ChannelID  int
	MasterAddr string
	RequestID  string
}

// SourcePreviewRequest 数据源预览请求参数
type SourcePreviewRequest struct {
	ChannelID int
}

// ConfigPreviewRequest 配置预览请求参数
type ConfigPreviewRequest struct {
	ChannelID  int
	MasterAddr string
}

// GetCheckItemByType 根据检查类型获取检查项与其索引
func (cp *CheckProgress) GetCheckItemByType(checkType CheckType) (*message.CheckItem, int) {
	for i := range cp.CheckItems {
		if cp.CheckItems[i].CheckType == string(checkType) {
			return &cp.CheckItems[i], i
		}
	}
	return nil, -1
}

// UpdateCheckItemByType 按类型更新检查项状态与进度
func (cp *CheckProgress) UpdateCheckItemByType(checkType CheckType, status string, progress int) {
	cp.Lock()
	defer cp.Unlock()
	if item, idx := cp.GetCheckItemByType(checkType); item != nil {
		cp.CheckItems[idx].Status = status
		cp.CheckItems[idx].Progress = progress
	}
}

// UpdateCheckItemErrorByType 按类型设置失败状态与错误信息/建议
func (cp *CheckProgress) UpdateCheckItemErrorByType(checkType CheckType, msg, errorMsg, suggestion string) {
	cp.Lock()
	defer cp.Unlock()
	if item, idx := cp.GetCheckItemByType(checkType); item != nil {
		cp.CheckItems[idx].Status = "failed"
		cp.CheckItems[idx].Progress = 100
		cp.CheckItems[idx].ErrorMsg = errorMsg
		cp.CheckItems[idx].Suggestion = suggestion
		cp.CheckItems[idx].Details = append(cp.CheckItems[idx].Details, message.CheckItemDetail{
			Type:       "error",
			Message:    msg,
			Suggestion: suggestion,
		})
	}
}

// AddCheckItemDetailByType 按类型添加详情
func (cp *CheckProgress) AddCheckItemDetailByType(checkType CheckType, detailType, detailMessage string, data message.CheckResultData, suggestion string) {
	cp.Lock()
	defer cp.Unlock()
	if item, idx := cp.GetCheckItemByType(checkType); item != nil {
		// 生成唯一ID
		detailID := fmt.Sprintf("%s_%s_%d", string(checkType), detailType, len(cp.CheckItems[idx].Details))

		// 确定分类
		category := cp.getCategoryByCheckType(checkType)

		// 确定严重程度
		severity := cp.getSeverityByType(detailType)

		cp.CheckItems[idx].Details = append(cp.CheckItems[idx].Details, message.CheckItemDetail{
			ID:         detailID,
			Type:       detailType,
			Category:   category,
			Title:      cp.generateTitle(detailType, detailMessage),
			Message:    detailMessage,
			Data:       data,
			Suggestion: suggestion,
			Timestamp:  time.Now().Format("2006-01-02 15:04:05"),
			Severity:   severity,
		})
	}
}

// getCategoryByCheckType 根据检查类型获取分类
func (cp *CheckProgress) getCategoryByCheckType(checkType CheckType) string {
	switch checkType {
	case CheckTypeTableConsistency:
		return "table"
	case CheckTypeWorkerBinding:
		return "binding"
	case CheckTypeSourcePermission:
		return "permission"
	default:
		return "general"
	}
}

// getSeverityByType 根据详情类型获取严重程度
func (cp *CheckProgress) getSeverityByType(detailType string) int {
	switch detailType {
	case "error":
		return 1 // 最高
	case "warn", "warning":
		return 2
	case "info":
		return 3
	case "success":
		return 4
	default:
		return 3
	}
}

// generateTitle 生成简短标题
func (cp *CheckProgress) generateTitle(detailType, message string) string {
	// 截取消息的前50个字符作为标题
	title := message
	if len(title) > 50 {
		title = title[:47] + "..."
	}
	return title
}

// UpdateCheckItemSummary 更新检查项汇总信息
func (cp *CheckProgress) UpdateCheckItemSummary(checkType CheckType) {
	cp.Lock()
	defer cp.Unlock()
	if item, idx := cp.GetCheckItemByType(checkType); item != nil {
		summary := message.CheckSummary{
			TotalItems: len(cp.CheckItems[idx].Details),
		}

		for _, detail := range cp.CheckItems[idx].Details {
			switch detail.Type {
			case "success":
				summary.PassedItems++
			case "error":
				summary.FailedItems++
			case "warn", "warning":
				summary.WarningItems++
			}
		}

		cp.CheckItems[idx].Summary = summary

		// 更新指标
		cp.CheckItems[idx].Metrics = message.CheckMetrics{
			Duration:      cp.calculateDuration(),
			LastUpdated:   time.Now().Format("2006-01-02 15:04:05"),
			CustomMetrics: make(map[string]interface{}),
		}
	}
}

// calculateDuration 计算持续时间
func (cp *CheckProgress) calculateDuration() string {
	if cp.EndTime != nil {
		duration := cp.EndTime.Sub(cp.StartTime)
		return duration.String()
	}
	duration := time.Since(cp.StartTime)
	return duration.String()
}

// CalculateOverallSummary 计算整体汇总信息
func (cp *CheckProgress) CalculateOverallSummary() message.OverallSummary {
	cp.RLock()
	defer cp.RUnlock()

	summary := message.OverallSummary{
		TotalChecks: len(cp.CheckItems),
	}

	for _, item := range cp.CheckItems {
		switch item.Status {
		case "success":
			summary.PassedChecks++
		case "failed", "error":
			summary.FailedChecks++
		case "warn", "warning":
			summary.WarningChecks++
		}

		// 统计严重问题
		for _, detail := range item.Details {
			if detail.Type == "error" && detail.Severity <= 2 {
				summary.CriticalIssues++
			}
		}
	}

	return summary
}

// CalculateHealthScore 计算健康评分
func (cp *CheckProgress) CalculateHealthScore() int {
	cp.RLock()
	defer cp.RUnlock()

	if len(cp.CheckItems) == 0 {
		return 100
	}

	totalScore := 0
	for _, item := range cp.CheckItems {
		itemScore := 100

		// 根据状态扣分
		switch item.Status {
		case "failed", "error":
			itemScore -= 50
		case "warn", "warning":
			itemScore -= 20
		}

		// 根据详情扣分
		for _, detail := range item.Details {
			switch detail.Type {
			case "error":
				if detail.Severity <= 2 {
					itemScore -= 10 // 严重错误
				} else {
					itemScore -= 5 // 一般错误
				}
			case "warn", "warning":
				itemScore -= 2
			}
		}

		if itemScore < 0 {
			itemScore = 0
		}
		totalScore += itemScore
	}

	return totalScore / len(cp.CheckItems)
}

// GenerateQuickActions 生成快速操作建议
func (cp *CheckProgress) GenerateQuickActions() []message.SuggestedAction {
	cp.RLock()
	defer cp.RUnlock()

	var actions []message.SuggestedAction

	for _, item := range cp.CheckItems {
		if item.Status == "failed" || item.Status == "error" {
			// 为失败的检查项生成修复建议
			if item.Suggestion != "" {
				actions = append(actions, message.SuggestedAction{
					Type:        "fix",
					Title:       fmt.Sprintf("Fix %s", item.Name),
					Description: item.Suggestion,
					Priority:    1,
				})
			}
		}

		// 从详情中提取操作建议
		for _, detail := range item.Details {
			if detail.Type == "error" && detail.Suggestion != "" {
				actions = append(actions, message.SuggestedAction{
					Type:        "fix",
					Title:       detail.Title,
					Description: detail.Suggestion,
					Priority:    detail.Severity,
				})
			}
		}
	}

	// 限制返回的操作数量
	if len(actions) > 5 {
		actions = actions[:5]
	}

	return actions
}

// GenerateRecommendations 生成优化建议
func (cp *CheckProgress) GenerateRecommendations() []string {
	cp.RLock()
	defer cp.RUnlock()

	var recommendations []string

	// 基于检查结果生成建议
	for _, item := range cp.CheckItems {
		switch item.CheckType {
		case string(CheckTypeTableConsistency):
			if item.Status != "success" {
				recommendations = append(recommendations, "建议定期检查表结构一致性，确保所有数据源的表结构保持同步")
			}
		case string(CheckTypeWorkerBinding):
			if item.Status != "success" {
				recommendations = append(recommendations, "建议优化DM集群配置，确保有足够的Worker节点处理数据源")
			}
		case string(CheckTypeSourcePermission):
			if item.Status != "success" {
				recommendations = append(recommendations, "建议检查并授予数据源用户必要的权限，确保数据同步正常运行")
			}
		}
	}

	// 通用建议
	healthScore := cp.CalculateHealthScore()
	if healthScore < 80 {
		recommendations = append(recommendations, "系统健康评分较低，建议及时处理检查中发现的问题")
	}

	return recommendations
}
