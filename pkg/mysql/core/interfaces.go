package core

import (
	"context"
	"time"

	"gitee.com/pingcap_enterprise/tms/server/message"
	"gitee.com/pingcap_enterprise/tms/server/models/channel"
	"gitee.com/pingcap_enterprise/tms/server/models/datasource"
)

// DMConverter 定义DM数据转换接口
type DMConverter interface {
	// 源配置转换
	ToCustomSourceConfig(dmConfig interface{}) *message.CustomSourceConfigDTO
	FromCustomSourceConfig(msgConfig *message.CustomSourceConfigDTO) interface{}
	
	// 数据库配置转换
	ToCustomDatabaseConfig(dmDBConfig interface{}) message.CustomDatabaseConfig
	FromCustomDatabaseConfig(msgDBConfig message.CustomDatabaseConfig) interface{}
}

// TaskConfigBuilder 定义任务配置构建接口
type TaskConfigBuilder interface {
	BuildTaskConfig(
		channelInfo *channel.ChannelInformation,
		targetDatasource *datasource.Datasource,
		sourceDatasources []*datasource.Datasource,
		schemaTables []*channel.ChannelSchemaTable,
		channelSchemas []*channel.ChannelSchema,
	) (*message.TaskConfigDTO, error)
}

// CheckManager 定义检查管理接口
type CheckManager interface {
	// 触发检查并返回RequestID
	TriggerCheck(ctx context.Context, channelId int, masterAddr string) (string, error)
	
	// 获取检查状态
	GetCheckStatus(requestID string) (*CheckProgress, error)
	
	// 清理过期的检查进度
	CleanupExpiredProgress(maxAge time.Duration) int
}

// ConfigPreviewer 定义配置预览接口  
type ConfigPreviewer interface {
	// 预览数据源配置
	PreviewSources(ctx context.Context, channelId int) (*message.PreviewSourcesResp, error)
	
	// 预览任务配置
	PreviewConfiguration(ctx context.Context, channelId int, masterAddr string) (*message.PreviewConfigurationResp, error)
	
	// 预览所有配置（树结构）
	PreviewAll(ctx context.Context, channelId int, masterAddr string) (*message.PreviewAllResp, error)
}
