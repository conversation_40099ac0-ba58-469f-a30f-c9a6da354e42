package mysql

import (
	"context"
	"sync"

	"gitee.com/pingcap_enterprise/tms/common/constants"
	"gitee.com/pingcap_enterprise/tms/pkg/mysql/checker"
	"gitee.com/pingcap_enterprise/tms/pkg/mysql/config"
	"gitee.com/pingcap_enterprise/tms/pkg/mysql/converter"
	"gitee.com/pingcap_enterprise/tms/pkg/mysql/core"
	"gitee.com/pingcap_enterprise/tms/pkg/mysql/dm"
	"gitee.com/pingcap_enterprise/tms/pkg/mysql/preview"
	"gitee.com/pingcap_enterprise/tms/server/message"
	"gitee.com/pingcap_enterprise/tms/server/models"
	"gitee.com/pingcap_enterprise/tms/server/models/datasource"
	"gitee.com/pingcap_enterprise/tms/util/log"
	"github.com/panjf2000/ants/v2"
)

// Service 新的MySQL服务层 - 轻量级编排层
type Service struct {
	// 核心业务组件
	converter       core.DMConverter
	configBuilder   core.TaskConfigBuilder
	checkManager    core.CheckManager
	configPreviewer core.ConfigPreviewer
}

// NewMySQLService 创建新的MySQL服务实例
func NewMySQLService() *Service {
	// 初始化核心组件
	dmConverter := converter.NewDMConverter()
	taskConfigBuilder := config.NewTaskConfigBuilder(dmConverter)
	checkManager := checker.NewCheckManager()
	configPreviewer := preview.NewConfigPreviewer(dmConverter, taskConfigBuilder)

	return &Service{
		converter:       dmConverter,
		configBuilder:   taskConfigBuilder,
		checkManager:    checkManager,
		configPreviewer: configPreviewer,
	}
}

// ListMembers 列出DM集群成员 - 纯API调用，无复杂业务逻辑
func (s *Service) ListMembers(ctx context.Context, req *message.ListMembersReq) (*message.ListMembersResp, error) {
	log.Infof("MySQL Service: Starting to list DM cluster members, masterAddr: %s", req.MasterAddr)

	dmAdaptor, err := dm.NewDMAdaptor(req.MasterAddr)
	if err != nil {
		log.Errorf("MySQL Service: Failed to create DM adaptor, masterAddr: %s, error: %v", req.MasterAddr, err)
		return nil, err
	}

	members, err := dmAdaptor.ListMember(ctx)
	if err != nil {
		log.Errorf("MySQL Service: Failed to get DM cluster members, masterAddr: %s, error: %v", req.MasterAddr, err)
		return nil, err
	}

	// 直接转换响应格式，无复杂业务逻辑
	resp := &message.ListMembersResp{
		Leader:  &message.Leader{Message: members.Leader.Message, Name: members.Leader.Name, Addr: members.Leader.Addr},
		Masters: make([]*message.Master, len(members.Masters)),
		Workers: make([]*message.Worker, len(members.Workers)),
		Msg:     members.Msg,
		Result:  members.Result,
	}

	for i, master := range members.Masters {
		resp.Masters[i] = &message.Master{
			Name: master.Name, MemberID: master.MemberID,
			Alive: master.Alive, PeerURLs: master.PeerURLs, ClientURLs: master.ClientURLs,
		}
	}

	for i, worker := range members.Workers {
		resp.Workers[i] = &message.Worker{
			Name: worker.Name, Addr: worker.Addr, Stage: worker.Stage, Source: worker.Source,
		}
	}

	log.Infof("MySQL Service: Successfully retrieved member list, masterAddr: %s, leader: %s, masters: %d, workers: %d",
		req.MasterAddr, resp.Leader.Name, len(resp.Masters), len(resp.Workers))
	return resp, nil
}

// PreviewConfiguration 预览配置 - 委托给配置预览器
func (s *Service) PreviewConfiguration(ctx context.Context, req *message.PreviewConfigurationReq) (*message.PreviewConfigurationResp, error) {
	log.Infof("MySQL Service: Preview configuration request, channelId: %d, masterAddr: %s", req.ChannelId, req.MasterAddr)
	return s.configPreviewer.PreviewConfiguration(ctx, req.ChannelId, req.MasterAddr)
}

// PreviewSources 预览数据源 - 委托给配置预览器
func (s *Service) PreviewSources(ctx context.Context, req *message.PreviewSourcesReq) (*message.PreviewSourcesResp, error) {
	log.Infof("MySQL Service: Preview sources request, channelId: %d", req.ChannelId)
	return s.configPreviewer.PreviewSources(ctx, req.ChannelId)
}

// PreviewAll 预览所有配置 - 委托给配置预览器
func (s *Service) PreviewAll(ctx context.Context, req *message.PreviewAllReq) (*message.PreviewAllResp, error) {
	log.Infof("MySQL Service: Preview all configurations request, channelId: %d, masterAddr: %s", req.ChannelId, req.MasterAddr)
	return s.configPreviewer.PreviewAll(ctx, req.ChannelId, req.MasterAddr)
}

// TriggerCheck 触发检查 - 委托给检查管理器
func (s *Service) TriggerCheck(ctx context.Context, req *message.TriggerCheckReq) (*message.TriggerCheckResp, error) {
	log.Infof("MySQL Service: Trigger check request, channelId: %d, masterAddr: %s", req.ChannelId, req.MasterAddr)

	requestID, err := s.checkManager.TriggerCheck(ctx, req.ChannelId, req.MasterAddr)
	if err != nil {
		return nil, err
	}

	return &message.TriggerCheckResp{
		RequestID: requestID,
	}, nil
}

// GetCheckStatus 获取检查状态 - 委托给检查管理器
func (s *Service) GetCheckStatus(ctx context.Context, req *message.GetCheckStatusReq) (*message.GetCheckStatusResp, error) {
	log.Infof("MySQL Service: Get check status request, requestID: %s", req.RequestID)

	progress, err := s.checkManager.GetCheckStatus(req.RequestID)
	if err != nil {
		return nil, err
	}

	// 更新所有检查项的汇总信息
	for _, item := range progress.CheckItems {
		progress.UpdateCheckItemSummary(core.CheckType(item.CheckType))
	}

	// 转换为响应格式
	resp := &message.GetCheckStatusResp{
		RequestID:       progress.RequestID,
		OverallStatus:   progress.OverallStatus,
		OverallProgress: progress.OverallProgress,
		CheckItems:      progress.CheckItems,
		StartTime:       progress.StartTime.Format(constants.TIME_FORMAT),

		// 新增字段
		Summary:         progress.CalculateOverallSummary(),
		QuickActions:    progress.GenerateQuickActions(),
		HealthScore:     progress.CalculateHealthScore(),
		Recommendations: progress.GenerateRecommendations(),
	}

	if progress.EndTime != nil {
		endTimeStr := progress.EndTime.Format(constants.TIME_FORMAT)
		resp.EndTime = endTimeStr
	}

	log.Infof("MySQL Service: Returning check status, requestID: %s, overall progress: %d%%", req.RequestID, progress.OverallProgress)
	return resp, nil
}

// ListSources 列出数据源 - 包含API调用和数据填充逻辑
func (s *Service) ListSources(ctx context.Context, req *message.ListSourcesReq) (*message.ListSourcesResp, error) {
	log.Infof("MySQL Service: List sources request, masterAddr: %s", req.MasterAddr)

	dmAdaptor, err := dm.NewDMAdaptor(req.MasterAddr)
	if err != nil {
		log.Errorf("MySQL Service: Failed to create DM adaptor, masterAddr: %s, error: %v", req.MasterAddr, err)
		return nil, err
	}

	// 获取所有数据源
	log.Infof("MySQL Service: Getting all sources from DM cluster, masterAddr: %s", req.MasterAddr)
	sources, err := dmAdaptor.ShowSource(ctx, []string{})
	if err != nil {
		log.Errorf("MySQL Service: Failed to get sources from DM cluster, masterAddr: %s, error: %v", req.MasterAddr, err)
		return nil, err
	}

	log.Infof("MySQL Service: Retrieved %d sources from DM cluster, masterAddr: %s", len(sources.Sources), req.MasterAddr)

	resp := &message.ListSourcesResp{
		Sources: make([]*message.Source, len(sources.Sources)),
		Msg:     sources.Msg,
		Result:  sources.Result,
	}

	// 转换每个数据源
	for i, source := range sources.Sources {
		resp.Sources[i] = &message.Source{
			Source: source.Source,
			Worker: source.Worker,
			Result: source.Result,
			Msg:    source.Msg,
		}
	}

	// 使用ants协程池优化配置获取
	if len(sources.Sources) > 0 {
		poolSize := 10
		if len(sources.Sources) < poolSize {
			poolSize = len(sources.Sources)
		}

		log.Infof("MySQL Service: Creating goroutine pool with size %d to fetch source configurations, masterAddr: %s", poolSize, req.MasterAddr)
		pool, err := ants.NewPool(poolSize)
		if err != nil {
			log.Warnf("MySQL Service: Failed to create goroutine pool, continuing without configuration enrichment, masterAddr: %s, error: %v", req.MasterAddr, err)
			return resp, nil
		}
		defer pool.Release()

		var wg sync.WaitGroup
		var mu sync.Mutex

		for i, source := range sources.Sources {
			if source.Source == "" {
				continue
			}

			wg.Add(1)
			idx := i
			sourceName := source.Source

			pool.Submit(func() {
				defer wg.Done()

				if config, configErr := dmAdaptor.GetSourceConfig(ctx, sourceName); configErr == nil {
					mu.Lock()
					resp.Sources[idx].Config = s.converter.ToCustomSourceConfig(config)
					mu.Unlock()
					log.Debugf("MySQL Service: Successfully retrieved source configuration: %s, masterAddr: %s", sourceName, req.MasterAddr)
				} else {
					log.Debugf("MySQL Service: Failed to get source configuration: %s, masterAddr: %s, error: %v", sourceName, req.MasterAddr, configErr)
				}
			})
		}

		wg.Wait()
		log.Infof("MySQL Service: Completed fetching all source configurations, masterAddr: %s", req.MasterAddr)
	}

	// 从数据库获取数据源以进行密码丰富
	log.Infof("MySQL Service: Getting datasources from database for password enrichment, masterAddr: %s", req.MasterAddr)
	datasources, _, err := models.GetDatasourceReaderWriter().List(ctx, 1, 1000, "", constants.DB_TYPE_MYSQL, "")
	if err != nil {
		log.Errorf("MySQL Service: Failed to get datasources from database, masterAddr: %s, error: %v", req.MasterAddr, err)
		return nil, err
	}
	log.Infof("MySQL Service: Retrieved %d datasources from database, masterAddr: %s", len(datasources), req.MasterAddr)

	// 从匹配的数据源填充密码
	if len(datasources) > 0 {
		log.Infof("MySQL Service: Filling source passwords from datasources, masterAddr: %s", req.MasterAddr)
		s.fillSourcePasswords(resp.Sources, datasources)
	}

	log.Infof("MySQL Service: Successfully completed, returning %d sources, masterAddr: %s", len(resp.Sources), req.MasterAddr)
	return resp, nil
}

// fillSourcePasswords 填充数据源密码
func (s *Service) fillSourcePasswords(sources []*message.Source, datasources []*datasource.Datasource) {
	log.Debugf("MySQL Service: fillSourcePasswords - Starting to fill passwords for %d sources using %d database datasources", len(sources), len(datasources))

	passwordsFilled := 0
	for _, source := range sources {
		if source.Config == nil {
			continue
		}

		// 使用严格的AND匹配查找匹配的数据源
		var matchedDatasource *datasource.Datasource
		for _, ds := range datasources {
			if source.Source == ds.DatasourceName &&
				source.Config.From.Host == ds.HostIp &&
				source.Config.From.Port == ds.HostPort {
				matchedDatasource = ds
				break
			}
		}

		// 如果找到匹配项，填充加密密码
		if matchedDatasource != nil && matchedDatasource.PasswordEncrypt != "" {
			source.Config.From.PasswordEncrypt = matchedDatasource.PasswordEncrypt
			passwordsFilled++
			log.Debugf("MySQL Service: fillSourcePasswords - Filled password for source: %s", source.Source)
		}
	}

	log.Infof("MySQL Service: fillSourcePasswords - Completed, filled passwords for %d sources", passwordsFilled)
}
