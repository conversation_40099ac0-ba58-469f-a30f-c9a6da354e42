package message

import "gitee.com/pingcap_enterprise/tms/pkg/mysql/dm"

type Leader struct {
	Name    string `json:"name"`
	Addr    string `json:"addr"`
	Message string `json:"message"`
}

type Master struct {
	Name       string   `json:"name"`
	MemberID   uint64   `json:"member_id"`
	Alive      bool     `json:"alive"`
	PeerURLs   []string `json:"peer_urls"`
	ClientURLs []string `json:"client_urls"`
}

type Worker struct {
	Name   string `json:"name"`
	Addr   string `json:"addr"`
	Stage  string `json:"stage"`
	Source string `json:"source"`
}

// ListMembersReq 列出成员请求结构体
type ListMembersReq struct {
	MasterAddr string `json:"masterAddr" form:"masterAddr" validate:"required"`
}

// ListMembersResp 列出成员响应结构体
type ListMembersResp struct {
	Leader  *Leader   `json:"leader"`
	Masters []*Master `json:"masters"`
	Workers []*Worker `json:"workers"`
	Msg     string    `json:"msg"`
	Result  bool      `json:"result"`
}

// ListSourcesReq 列出数据源请求结构体
type ListSourcesReq struct {
	MasterAddr string `json:"masterAddr" form:"masterAddr" validate:"required"`
}

// PreviewSourcesReq 根据ChannelId对数据源进行预览，输出为DM格式
type PreviewSourcesReq struct {
	ChannelId int `json:"channelId" form:"channelId" validate:"required"`
}
type PreviewConfigurationReq struct {
	ChannelId  int    `json:"channelId" form:"channelId" validate:"required"`
	MasterAddr string `json:"masterAddr" form:"masterAddr" validate:"required"`
}

type PreviewSourcesResp struct {
	Configs []CustomSourceConfigDTO `json:"configs"`
}

type PreviewConfigurationResp struct {
	Config TaskConfigDTO `json:"config"`
}

// ListSourcesResp 列出数据源响应结构体
type ListSourcesResp struct {
	Sources []*Source `json:"sources"`
	Msg     string    `json:"msg"`
	Result  bool      `json:"result"`
}

// Source 数据源信息结构体
type Source struct {
	Source string                 `json:"source"`
	Worker string                 `json:"worker"`
	Result bool                   `json:"result"`
	Msg    string                 `json:"msg"`
	Config *CustomSourceConfigDTO `json:"config,omitempty"`
}

// CustomSourceConfigDTO 自定义数据源配置 DTO 结构，增加密码加密字段
type CustomSourceConfigDTO struct {
	SourceID string `json:"source_id"`

	EnableGTID bool `json:"enable_gtid"`

	EnableRelay     bool   `json:"enable_relay"`
	RelayBinlogName string `json:"relay_binlog_name"`
	RelayBinlogGTID string `json:"relay_binlog_gtid"`

	From CustomDatabaseConfig `json:"from"`

	Purge *dm.PurgeConfig `json:"purge,omitempty"`

	Checker *dm.CheckerConfig `json:"checker,omitempty"`

	CaseSensitive bool             `json:"case_sensitive"`
	Filters       []dm.EventFilter `json:"filters,omitempty"`

	RelayDir string `json:"relay_dir"`
}

// CustomDatabaseConfig 自定义数据库连接配置，增加密码加密字段
type CustomDatabaseConfig struct {
	Host            string             `json:"host"`
	Port            int                `json:"port"`
	User            string             `json:"user"`
	Password        string             `json:"password"`
	PasswordEncrypt string             `json:"password_encrypt"`
	Security        *dm.SecurityConfig `json:"security,omitempty"`
}

// PreviewAllReq 预览所有配置请求结构体
type PreviewAllReq struct {
	ChannelId  int    `json:"channelId" form:"channelId" validate:"required"`
	MasterAddr string `json:"masterAddr" form:"masterAddr" validate:"required"`
}

// PreviewAllResp 预览所有配置响应结构体
type PreviewAllResp struct {
	Tree *DMConfigTreeNode `json:"tree"`
}

// DMConfigTreeNode DM配置树节点结构体
type DMConfigTreeNode struct {
	Key      string              `json:"key"`                // 唯一标识符
	Title    string              `json:"title"`              // 显示名称（文件名或节点名）
	Context  string              `json:"context,omitempty"`  // YAML格式的配置内容
	Children []*DMConfigTreeNode `json:"children,omitempty"` // 子节点
	IsLeaf   bool                `json:"isLeaf"`             // 是否为叶子节点
}

// TriggerCheckReq 触发检查请求结构体
type TriggerCheckReq struct {
	ChannelId  int    `json:"channelId" form:"channelId" validate:"required"`   // 通道ID
	MasterAddr string `json:"masterAddr" form:"masterAddr" validate:"required"` // DM Master地址
}

// TriggerCheckResp 触发检查响应结构体
type TriggerCheckResp struct {
	RequestID string `json:"requestId"` // 检查请求唯一标识符
}

// GetCheckStatusReq 获取检查状态请求结构体
type GetCheckStatusReq struct {
	RequestID string `json:"requestId" form:"requestId" validate:"required"` // 检查请求唯一标识符
}

// CheckItem 检查项结构体
type CheckItem struct {
	CheckType  string            `json:"checkType"`  // 检查类型标识
	Name       string            `json:"name"`       // 检查项名称
	Status     string            `json:"status"`     // 检查状态：pending, running, success, failed
	Progress   int               `json:"progress"`   // 进度百分比 (0-100)
	Details    []CheckItemDetail `json:"details"`    // 检查详情
	Suggestion string            `json:"suggestion"` // 修复建议
	ErrorMsg   string            `json:"errorMsg"`   // 错误信息

	// 新增字段
	Summary CheckSummary      `json:"summary"` // 检查汇总信息
	Metrics CheckMetrics      `json:"metrics"` // 检查指标
	Actions []SuggestedAction `json:"actions"` // 建议操作
}

// CheckItemDetail 检查项详情结构体
type CheckItemDetail struct {
	ID         string          `json:"id"`         // 唯一标识
	Type       string          `json:"type"`       // 详情类型：info, warning, error, success
	Category   string          `json:"category"`   // 分类：binding, permission, table等
	Title      string          `json:"title"`      // 简短标题
	Message    string          `json:"message"`    // 详细消息
	Data       CheckResultData `json:"data"`       // 结构化数据
	Suggestion string          `json:"suggestion"` // 修复建议
	Timestamp  string          `json:"timestamp"`  // 时间戳
	Severity   int             `json:"severity"`   // 严重程度 1-5，1最高
}

// CheckSummary 检查汇总信息
type CheckSummary struct {
	TotalItems   int `json:"totalItems"`
	PassedItems  int `json:"passedItems"`
	FailedItems  int `json:"failedItems"`
	WarningItems int `json:"warningItems"`
}

// CheckMetrics 检查指标
type CheckMetrics struct {
	Duration      string                 `json:"duration"`
	LastUpdated   string                 `json:"lastUpdated"`
	CustomMetrics map[string]interface{} `json:"customMetrics"`
}

// SuggestedAction 建议操作
type SuggestedAction struct {
	Type        string `json:"type"` // fix, warning, info
	Title       string `json:"title"`
	Description string `json:"description"`
	Command     string `json:"command,omitempty"`
	Priority    int    `json:"priority"` // 1-5, 1最高
}

// OverallSummary 整体汇总
type OverallSummary struct {
	TotalChecks    int `json:"totalChecks"`
	PassedChecks   int `json:"passedChecks"`
	FailedChecks   int `json:"failedChecks"`
	WarningChecks  int `json:"warningChecks"`
	CriticalIssues int `json:"criticalIssues"`
}

// GetCheckStatusResp 获取检查状态响应结构体
type GetCheckStatusResp struct {
	RequestID       string      `json:"requestId"`       // 检查请求唯一标识符
	OverallStatus   string      `json:"overallStatus"`   // 整体状态：pending, running, completed, failed
	OverallProgress int         `json:"overallProgress"` // 整体进度百分比 (0-100)
	CheckItems      []CheckItem `json:"checkItems"`      // 检查项列表
	StartTime       string      `json:"startTime"`       // 开始时间
	EndTime         string      `json:"endTime"`         // 结束时间（如果已完成）

	// 新增前端友好字段
	Summary         OverallSummary    `json:"summary"`         // 整体汇总
	QuickActions    []SuggestedAction `json:"quickActions"`    // 快速操作建议
	HealthScore     int               `json:"healthScore"`     // 健康评分 0-100
	Recommendations []string          `json:"recommendations"` // 优化建议
}

// TableInfo 表信息结构体，用于检查表一致性
type TableInfo struct {
	SchemaName string   `json:"schemaName"` // 数据库名
	TableName  string   `json:"tableName"`  // 表名
	Columns    []string `json:"columns"`    // 列名列表
}

// WorkerBindingInfo Worker绑定信息结构体
type WorkerBindingInfo struct {
	WorkerName string `json:"workerName"` // Worker名称
	SourceName string `json:"sourceName"` // 绑定的数据源名称（空表示未绑定）
	Status     string `json:"status"`     // 绑定状态：bound, idle
}

// 实现CheckResultData接口
func (w WorkerBindingInfo) GetType() string {
	return "worker_binding_info"
}

func (w WorkerBindingInfo) GetSummary() map[string]interface{} {
	return map[string]interface{}{
		"workerName": w.WorkerName,
		"sourceName": w.SourceName,
		"status":     w.Status,
	}
}

// SourcePermissionInfo 数据源权限信息结构体
type SourcePermissionInfo struct {
	SourceName        string   `json:"sourceName"`        // 数据源名称
	HasPermission     bool     `json:"hasPermission"`     // 是否有足够权限
	MissingPrivileges []string `json:"missingPrivileges"` // 缺失的权限列表
}

// 实现CheckResultData接口
func (s SourcePermissionInfo) GetType() string {
	return "source_permission_info"
}

func (s SourcePermissionInfo) GetSummary() map[string]interface{} {
	return map[string]interface{}{
		"sourceName":        s.SourceName,
		"hasPermission":     s.HasPermission,
		"missingPrivileges": len(s.MissingPrivileges),
	}
}

// ===== 结构化检查结果数据类型 =====

// CheckResultData 统一的检查结果数据接口
type CheckResultData interface {
	GetType() string
	GetSummary() map[string]interface{}
}

// TableConsistencyData 表一致性检查结果数据
type TableConsistencyData struct {
	TotalTables        int                    `json:"totalTables"`
	CheckedTables      int                    `json:"checkedTables"`
	InconsistentTables []TableInconsistency   `json:"inconsistentTables"`
	SchemaDetails      []SchemaCheckDetail    `json:"schemaDetails"`
	ConsistentTables   []TableConsistencyInfo `json:"consistentTables"`
}

func (t TableConsistencyData) GetType() string {
	return "table_consistency"
}

func (t TableConsistencyData) GetSummary() map[string]interface{} {
	return map[string]interface{}{
		"totalTables":       t.TotalTables,
		"checkedTables":     t.CheckedTables,
		"inconsistentCount": len(t.InconsistentTables),
		"consistentCount":   len(t.ConsistentTables),
		"consistencyRate":   float64(len(t.ConsistentTables)) / float64(t.TotalTables) * 100,
	}
}

// TableInconsistency 表不一致信息
type TableInconsistency struct {
	SchemaName  string   `json:"schemaName"`
	TableName   string   `json:"tableName"`
	Issues      []string `json:"issues"`
	Datasources []string `json:"datasources"`
	Severity    string   `json:"severity"` // critical, warning, info
}

// SchemaCheckDetail 模式检查详情
type SchemaCheckDetail struct {
	SchemaName    string `json:"schemaName"`
	TableCount    int    `json:"tableCount"`
	CheckedTables int    `json:"checkedTables"`
	Status        string `json:"status"` // consistent, inconsistent, partial
}

// TableConsistencyInfo 表一致性信息
type TableConsistencyInfo struct {
	SchemaName  string   `json:"schemaName"`
	TableName   string   `json:"tableName"`
	Datasources []string `json:"datasources"`
	Status      string   `json:"status"` // consistent
}

// WorkerBindingData Worker绑定检查结果数据
type WorkerBindingData struct {
	TotalSources   int                   `json:"totalSources"`
	BoundSources   int                   `json:"boundSources"`
	UnboundSources int                   `json:"unboundSources"`
	TotalWorkers   int                   `json:"totalWorkers"`
	IdleWorkers    int                   `json:"idleWorkers"`
	BindingDetails []WorkerBindingDetail `json:"bindingDetails"`
	ClusterSummary ClusterSummary        `json:"clusterSummary"`
}

func (w WorkerBindingData) GetType() string {
	return "worker_binding"
}

func (w WorkerBindingData) GetSummary() map[string]interface{} {
	bindingRate := float64(w.BoundSources) / float64(w.TotalSources) * 100
	if w.TotalSources == 0 {
		bindingRate = 100
	}
	return map[string]interface{}{
		"totalSources":   w.TotalSources,
		"boundSources":   w.BoundSources,
		"unboundSources": w.UnboundSources,
		"totalWorkers":   w.TotalWorkers,
		"idleWorkers":    w.IdleWorkers,
		"bindingRate":    bindingRate,
		"hasCapacity":    w.IdleWorkers >= w.UnboundSources,
	}
}

// WorkerBindingDetail Worker绑定详情
type WorkerBindingDetail struct {
	SourceName    string `json:"sourceName"`
	DatasourceId  int    `json:"datasourceId"`
	DbType        string `json:"dbType"`
	BindingStatus string `json:"bindingStatus"` // bound, unbound, error
	WorkerName    string `json:"workerName"`
	WorkerAddr    string `json:"workerAddr"`
	Message       string `json:"message"`
}

// ClusterSummary 集群汇总信息
type ClusterSummary struct {
	LeaderName    string `json:"leaderName"`
	MasterCount   int    `json:"masterCount"`
	WorkerCount   int    `json:"workerCount"`
	BoundWorkers  int    `json:"boundWorkers"`
	IdleWorkers   int    `json:"idleWorkers"`
	ClusterHealth string `json:"clusterHealth"` // healthy, warning, critical
}

// PermissionCheckData 权限检查结果数据
type PermissionCheckData struct {
	TotalSources      int                      `json:"totalSources"`
	PassedSources     int                      `json:"passedSources"`
	FailedSources     int                      `json:"failedSources"`
	PermissionDetails []SourcePermissionDetail `json:"permissionDetails"`
	MissingPrivileges []string                 `json:"missingPrivileges"`
	FixCommands       []string                 `json:"fixCommands"`
}

func (p PermissionCheckData) GetType() string {
	return "permission_check"
}

func (p PermissionCheckData) GetSummary() map[string]interface{} {
	passRate := float64(p.PassedSources) / float64(p.TotalSources) * 100
	if p.TotalSources == 0 {
		passRate = 100
	}
	return map[string]interface{}{
		"totalSources":       p.TotalSources,
		"passedSources":      p.PassedSources,
		"failedSources":      p.FailedSources,
		"passRate":           passRate,
		"uniquePrivileges":   len(p.MissingPrivileges),
		"hasPermissionIssue": p.FailedSources > 0,
	}
}

// SourcePermissionDetail 数据源权限详情
type SourcePermissionDetail struct {
	SourceName        string   `json:"sourceName"`
	DatasourceId      int      `json:"datasourceId"`
	DbType            string   `json:"dbType"`
	HasPermission     bool     `json:"hasPermission"`
	MissingPrivileges []string `json:"missingPrivileges"`
	CurrentUser       string   `json:"currentUser"`
	CurrentHost       string   `json:"currentHost"`
	ConnectionStatus  string   `json:"connectionStatus"` // connected, failed
	ErrorMessage      string   `json:"errorMessage,omitempty"`
}

// 前端展示优化的数据结构

// DisplayTableData 为前端表格展示优化的数据结构
type DisplayTableData struct {
	Headers []TableHeader `json:"headers"`
	Rows    []TableRow    `json:"rows"`
	Actions []RowAction   `json:"actions"`
}

// TableHeader 表格头部
type TableHeader struct {
	Key        string `json:"key"`
	Title      string `json:"title"`
	Sortable   bool   `json:"sortable"`
	Filterable bool   `json:"filterable"`
}

// TableRow 表格行
type TableRow struct {
	ID      string                 `json:"id"`
	Data    map[string]interface{} `json:"data"`
	Status  string                 `json:"status"` // success, warning, error
	Actions []string               `json:"actions"`
}

// RowAction 行操作
type RowAction struct {
	Key   string `json:"key"`
	Title string `json:"title"`
	Type  string `json:"type"` // primary, secondary, danger
}

// ChartData 图表数据
type ChartData struct {
	Type   string      `json:"type"` // pie, bar, line
	Title  string      `json:"title"`
	Data   interface{} `json:"data"`
	Config ChartConfig `json:"config"`
}

// ChartConfig 图表配置
type ChartConfig struct {
	Colors     []string `json:"colors"`
	ShowLegend bool     `json:"showLegend"`
	Height     int      `json:"height"`
}
